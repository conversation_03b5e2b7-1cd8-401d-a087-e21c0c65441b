"""
重构后的推理演示脚本
使用新的模块化架构进行推理
"""

import argparse
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

from src.core import get_logger, setup_logger, ExceptionContext
from src.inference import create_predictor, create_inference_demo
from src.utils import validate_image_file

# 设置日志
logger = setup_logger("inference", level="INFO")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="导航模型推理演示")
    
    parser.add_argument(
        "--model_path", type=str, required=True,
        help="模型路径"
    )
    parser.add_argument(
        "--image_path", type=str, required=True,
        help="图像路径"
    )
    parser.add_argument(
        "--question", type=str, required=True,
        help="问题文本"
    )
    parser.add_argument(
        "--max_new_tokens", type=int, default=256,
        help="最大新token数"
    )
    parser.add_argument(
        "--temperature", type=float, default=0.7,
        help="温度参数"
    )
    parser.add_argument(
        "--device", type=str, default=None,
        help="设备（cuda/cpu）"
    )
    parser.add_argument(
        "--output_file", type=str,
        help="结果输出文件"
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    try:
        # 解析参数
        args = parse_args()
        
        logger.info("开始推理演示...")
        logger.info(f"模型: {args.model_path}")
        logger.info(f"图像: {args.image_path}")
        logger.info(f"问题: {args.question}")
        
        # 验证输入
        if not validate_image_file(args.image_path):
            logger.error("图像文件验证失败")
            return 1
        
        # 创建预测器
        logger.info("加载模型...")
        with create_predictor(
            model_path=args.model_path,
            device=args.device
        ) as predictor:
            
            # 显示模型信息
            model_info = predictor.get_model_info()
            logger.info(f"模型类型: {model_info.get('model_type', 'Unknown')}")
            
            # 显示内存使用
            memory_info = predictor.get_memory_usage()
            if memory_info:
                logger.info(f"GPU内存使用: {memory_info.get('gpu_memory_allocated', 0):.1f}GB")
            
            # 执行推理
            logger.info("执行推理...")
            result = predictor.predict(
                image=args.image_path,
                question=args.question,
                max_new_tokens=args.max_new_tokens,
                temperature=args.temperature
            )
            
            # 显示结果
            print("\n" + "="*60)
            print("推理结果")
            print("="*60)
            print(f"输入图像: {args.image_path}")
            print(f"问题: {args.question}")
            print("-"*60)
            print(f"原始输出: {result['raw_output']}")
            print(f"是否为导航动作: {result['is_navigation']}")
            
            if result['is_navigation']:
                print(f"导航动作: {result['action']}")
            else:
                print(f"文本答案: {result['answer']}")
            
            print("="*60)
            
            # 保存结果
            if args.output_file:
                import json
                output_data = {
                    "model_path": args.model_path,
                    "image_path": args.image_path,
                    "question": args.question,
                    "result": result,
                    "model_info": model_info,
                    "memory_info": memory_info
                }
                
                with open(args.output_file, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"结果已保存到: {args.output_file}")
        
        logger.info("推理演示完成！")
        return 0
        
    except Exception as e:
        logger.error(f"推理失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
