"""
重构后的数据预处理脚本
使用新的模块化架构进行数据预处理
"""

import argparse
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

from src.core import get_logger, setup_logger, ExceptionContext
from src.data import NavigationDataLoader, NavigationProcessor
from src.utils import safe_save_json, ensure_dir

# 设置日志
logger = setup_logger("preprocessing", level="INFO")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据预处理")
    
    parser.add_argument(
        "--data_root", type=str, required=True,
        help="数据根目录"
    )
    parser.add_argument(
        "--split_file", type=str, required=True,
        help="数据分割文件"
    )
    parser.add_argument(
        "--output_dir", type=str, default="processed_data",
        help="输出目录"
    )
    parser.add_argument(
        "--splits", nargs="+", default=["train", "val", "test"],
        help="要处理的数据分割"
    )
    parser.add_argument(
        "--max_samples_per_split", type=int, default=-1,
        help="每个分割的最大样本数，-1表示全部"
    )
    parser.add_argument(
        "--validate_data", action="store_true",
        help="验证数据有效性"
    )
    parser.add_argument(
        "--save_processed", action="store_true",
        help="保存预处理后的数据"
    )
    parser.add_argument(
        "--image_size", nargs=2, type=int, default=[224, 224],
        help="图像尺寸"
    )
    parser.add_argument(
        "--max_length", type=int, default=2048,
        help="最大序列长度"
    )
    
    return parser.parse_args()


def analyze_data_distribution(samples, split_name):
    """分析数据分布"""
    logger.info(f"\n=== {split_name.upper()} 数据分析 ===")
    
    # 基本统计
    total_samples = len(samples)
    logger.info(f"总样本数: {total_samples}")
    
    if total_samples == 0:
        return
    
    # 动作分布
    action_counts = {}
    scene_counts = {}
    step_counts = []
    
    for sample in samples:
        # 统计动作
        action = sample.get("action", "unknown")
        action_counts[action] = action_counts.get(action, 0) + 1
        
        # 统计场景
        scene = sample.get("scene_name", "unknown")
        scene_counts[scene] = scene_counts.get(scene, 0) + 1
        
        # 统计步数
        total_steps = sample.get("total_steps", 0)
        if total_steps > 0:
            step_counts.append(total_steps)
    
    # 显示动作分布
    logger.info("动作分布:")
    for action, count in sorted(action_counts.items()):
        percentage = (count / total_samples) * 100
        logger.info(f"  {action}: {count} ({percentage:.1f}%)")
    
    # 显示场景分布
    logger.info(f"场景数量: {len(scene_counts)}")
    top_scenes = sorted(scene_counts.items(), key=lambda x: x[1], reverse=True)[:5]
    logger.info("前5个场景:")
    for scene, count in top_scenes:
        logger.info(f"  {scene}: {count} 样本")
    
    # 显示路径长度统计
    if step_counts:
        import numpy as np
        logger.info(f"路径长度统计:")
        logger.info(f"  平均: {np.mean(step_counts):.1f}")
        logger.info(f"  中位数: {np.median(step_counts):.1f}")
        logger.info(f"  最小: {min(step_counts)}")
        logger.info(f"  最大: {max(step_counts)}")


def validate_samples(samples, split_name):
    """验证样本数据"""
    logger.info(f"验证 {split_name} 数据...")
    
    valid_samples = []
    invalid_count = 0
    
    required_fields = ["image_path", "question", "target"]
    
    for i, sample in enumerate(samples):
        is_valid = True
        
        # 检查必需字段
        for field in required_fields:
            if field not in sample or not sample[field]:
                logger.warning(f"样本 {i}: 缺少字段 '{field}'")
                is_valid = False
        
        # 检查图像文件
        if "image_path" in sample:
            image_path = sample["image_path"]
            if not os.path.exists(image_path):
                logger.warning(f"样本 {i}: 图像文件不存在 {image_path}")
                is_valid = False
        
        if is_valid:
            valid_samples.append(sample)
        else:
            invalid_count += 1
    
    logger.info(f"验证完成: {len(valid_samples)} 有效, {invalid_count} 无效")
    return valid_samples


def main():
    """主函数"""
    try:
        # 解析参数
        args = parse_args()
        
        logger.info("开始数据预处理...")
        logger.info(f"数据根目录: {args.data_root}")
        logger.info(f"分割文件: {args.split_file}")
        logger.info(f"输出目录: {args.output_dir}")
        
        # 确保输出目录存在
        ensure_dir(args.output_dir)
        
        # 创建数据加载器
        data_loader = NavigationDataLoader(
            data_root=args.data_root,
            split_file=args.split_file
        )
        
        # 创建数据处理器
        processor = NavigationProcessor(
            image_size=tuple(args.image_size),
            max_length=args.max_length
        )
        
        # 获取数据集统计信息
        stats = data_loader.get_statistics()
        logger.info(f"数据集统计: {stats}")
        
        # 处理每个分割
        all_results = {}
        
        for split in args.splits:
            logger.info(f"\n处理 {split} 分割...")
            
            try:
                # 获取场景列表
                scene_list = data_loader.get_scene_list(split)
                logger.info(f"{split} 分割包含 {len(scene_list)} 个场景")
                
                # 加载所有样本
                all_samples = []
                
                for scene_name in scene_list:
                    logger.info(f"处理场景: {scene_name}")
                    
                    # 加载场景数据
                    scene_data = data_loader.load_scene_data(scene_name)
                    
                    if not scene_data:
                        logger.warning(f"场景 {scene_name} 无数据")
                        continue
                    
                    # 查找场景目录
                    scene_dir = None
                    for dataset_type in ["HM3D", "Matterport"]:
                        potential_dir = os.path.join(args.data_root, dataset_type, scene_name)
                        if os.path.exists(potential_dir):
                            scene_dir = potential_dir
                            break
                    
                    if not scene_dir:
                        logger.warning(f"未找到场景目录: {scene_name}")
                        continue
                    
                    # 提取样本
                    scene_samples = processor.extract_samples_from_scene_data(
                        scene_data, scene_dir
                    )
                    
                    # 添加场景信息
                    for sample in scene_samples:
                        sample["scene_name"] = scene_name
                    
                    all_samples.extend(scene_samples)
                    logger.info(f"场景 {scene_name}: {len(scene_samples)} 样本")
                
                # 限制样本数量
                if args.max_samples_per_split > 0 and len(all_samples) > args.max_samples_per_split:
                    all_samples = all_samples[:args.max_samples_per_split]
                    logger.info(f"限制样本数量到: {args.max_samples_per_split}")
                
                # 验证数据
                if args.validate_data:
                    all_samples = validate_samples(all_samples, split)
                
                # 分析数据分布
                analyze_data_distribution(all_samples, split)
                
                # 保存结果
                split_result = {
                    "split": split,
                    "total_samples": len(all_samples),
                    "scenes": scene_list,
                    "samples": all_samples if args.save_processed else []
                }
                
                all_results[split] = split_result
                
                # 保存分割数据
                split_output_file = os.path.join(args.output_dir, f"{split}_processed.json")
                safe_save_json(split_result, split_output_file)
                logger.info(f"{split} 数据已保存到: {split_output_file}")
                
            except Exception as e:
                logger.error(f"处理 {split} 分割时出错: {e}")
                continue
        
        # 保存汇总信息
        summary = {
            "data_root": args.data_root,
            "split_file": args.split_file,
            "processing_args": vars(args),
            "splits": {split: {"total_samples": result["total_samples"], 
                              "scenes": len(result["scenes"])}
                      for split, result in all_results.items()}
        }
        
        summary_file = os.path.join(args.output_dir, "preprocessing_summary.json")
        safe_save_json(summary, summary_file)
        
        logger.info(f"\n=== 预处理完成 ===")
        logger.info(f"汇总信息已保存到: {summary_file}")
        
        for split, info in summary["splits"].items():
            logger.info(f"{split}: {info['total_samples']} 样本, {info['scenes']} 场景")
        
        return 0
        
    except Exception as e:
        logger.error(f"数据预处理失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
