"""
精简的数据加载器 - 只保留核心功能
"""

import os
import json
from torch.utils.data import Dataset
from qwen_vl_utils import process_vision_info


class NavigationDataset(Dataset):
    """精简的导航数据集"""

    def __init__(self, data_root, split_file, split="train", max_samples=-1):
        self.data_root = data_root
        self.split = split
        self.max_samples = max_samples

        # 加载数据分割
        with open(split_file, "r") as f:
            split_data = json.load(f)

        # 获取当前分割的文件列表
        file_list = split_data.get(split, [])
        if max_samples > 0:
            file_list = file_list[:max_samples]

        # 加载所有样本
        self.samples = []
        for file_name in file_list:
            file_path = os.path.join(data_root, file_name)
            if os.path.exists(file_path):
                with open(file_path, "r") as f:
                    data = json.load(f)
                    self.samples.extend(self._process_file_data(data, file_name))

        print(f"加载了 {len(self.samples)} 个样本")

    def _process_file_data(self, data, _):
        """处理单个文件的数据"""
        samples = []
        scene_name = data.get("scene_name", "")
        question = data.get("question", "")
        answer = data.get("answer", "")
        path = data.get("path", [])

        scene_dir = os.path.join(self.data_root, scene_name)

        for step in path:
            img_path = os.path.join(scene_dir, step.get("img", ""))
            action = step.get("action", "")

            if not os.path.exists(img_path):
                continue

            # 构建目标输出
            if action in ["move_forward", "turn_left", "turn_right"]:
                target = f"<|action_{action}|>"
            elif action == "stop":
                target = answer
            else:
                continue

            sample = {
                "image_path": img_path,
                "question": question,
                "target": target,
                "action": action,
                "scene_name": scene_name,
            }
            samples.append(sample)

        return samples

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]

        # 创建messages格式
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": sample["image_path"]},
                    {"type": "text", "text": sample["question"]},
                ],
            },
            {"role": "assistant", "content": sample["target"]},
        ]

        return {
            "messages": messages,
            "image_path": sample["image_path"],
            "target": sample["target"],
        }


def collate_fn(batch, processor):
    """数据整理函数"""
    # 提取messages
    messages_list = [item["messages"] for item in batch]

    # 使用processor处理
    texts = [
        processor.apply_chat_template(msg, tokenize=False, add_generation_prompt=False)
        for msg in messages_list
    ]

    image_inputs, video_inputs = process_vision_info(messages_list)

    inputs = processor(
        text=texts,
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )

    return inputs
