"""
推理工具函数模块

提供推理相关的实用工具函数
"""

import torch
from typing import Dict, Any, Union, List, Optional
from PIL import Image
import logging

from ..core import get_logger, InferenceError, ExceptionContext
from ..core.constants import NAVIGATION_TOKENS, TOKEN_ACTION_MAP

logger = get_logger(__name__)

# 尝试导入官方工具
try:
    from qwen_vl_utils import process_vision_info
    QWEN_VL_UTILS_AVAILABLE = True
except ImportError:
    QWEN_VL_UTILS_AVAILABLE = False


def load_inference_model(
    model_path: str,
    device: Optional[str] = None,
    torch_dtype: str = "bfloat16",
    **kwargs
):
    """
    加载推理模型
    
    Args:
        model_path: 模型路径
        device: 设备
        torch_dtype: 数据类型
        **kwargs: 其他参数
        
    Returns:
        模型组件元组
    """
    with ExceptionContext(f"Loading inference model: {model_path}"):
        try:
            from ..models import create_model
            
            # 创建模型
            model_wrapper = create_model(
                model_name=model_path,
                device_map=device or "auto",
                torch_dtype=torch_dtype,
                **kwargs
            )
            
            # 加载所有组件
            model_wrapper.load_all()
            model_wrapper.prepare_for_inference()
            
            return (
                model_wrapper.get_model(),
                model_wrapper.get_tokenizer(),
                model_wrapper.get_processor()
            )
            
        except Exception as e:
            raise InferenceError(f"Failed to load inference model: {e}", cause=e)


def prepare_inference_input(
    image: Union[str, Image.Image],
    question: str,
    processor,
    device: str = "cuda"
) -> Dict[str, torch.Tensor]:
    """
    准备推理输入
    
    Args:
        image: 图像路径或PIL图像
        question: 问题文本
        processor: 处理器
        device: 设备
        
    Returns:
        Dict[str, torch.Tensor]: 处理后的输入
    """
    with ExceptionContext("Preparing inference input"):
        try:
            # 加载图像
            if isinstance(image, str):
                image = Image.open(image).convert("RGB")
            elif not isinstance(image, Image.Image):
                raise ValueError(f"Unsupported image type: {type(image)}")
            
            # 构建messages格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": question},
                    ],
                }
            ]
            
            # 使用官方工具处理（如果可用）
            if QWEN_VL_UTILS_AVAILABLE:
                return _prepare_input_with_official_tools(messages, processor, device)
            else:
                return _prepare_input_simple(image, question, processor, device)
                
        except Exception as e:
            raise InferenceError(f"Failed to prepare inference input: {e}", cause=e)


def _prepare_input_with_official_tools(
    messages: List[Dict[str, Any]],
    processor,
    device: str
) -> Dict[str, torch.Tensor]:
    """使用官方工具准备输入"""
    try:
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        # 移动到设备
        inputs = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                 for k, v in inputs.items()}
        
        return inputs
        
    except Exception as e:
        logger.warning(f"Official tools failed, falling back to simple mode: {e}")
        raise


def _prepare_input_simple(
    image: Image.Image,
    question: str,
    processor,
    device: str
) -> Dict[str, torch.Tensor]:
    """简单模式准备输入"""
    try:
        # 简单的文本处理
        text = f"User: {question}\nAssistant:"
        
        # 分别处理图像和文本
        text_inputs = processor.tokenizer(
            text,
            return_tensors="pt",
            padding=True
        )
        
        # 处理图像（如果处理器支持）
        if hasattr(processor, 'image_processor'):
            image_inputs = processor.image_processor(
                images=[image],
                return_tensors="pt"
            )
            
            # 合并输入
            inputs = {**text_inputs, **image_inputs}
        else:
            inputs = text_inputs
        
        # 移动到设备
        inputs = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                 for k, v in inputs.items()}
        
        return inputs
        
    except Exception as e:
        raise InferenceError(f"Simple input preparation failed: {e}", cause=e)


def parse_navigation_output(
    raw_output: str,
    nav_tokenizer=None
) -> Dict[str, Any]:
    """
    解析导航输出
    
    Args:
        raw_output: 原始输出文本
        nav_tokenizer: 导航分词器
        
    Returns:
        Dict[str, Any]: 解析结果
    """
    result = {
        "is_navigation": False,
        "action": None,
        "answer": raw_output.strip()
    }
    
    try:
        # 检查是否包含导航Token
        for token in NAVIGATION_TOKENS:
            if token in raw_output:
                result["is_navigation"] = True
                result["action"] = TOKEN_ACTION_MAP.get(token)
                break
        
        # 如果不是导航动作，保留原始答案
        if not result["is_navigation"]:
            result["answer"] = raw_output.strip()
        else:
            # 如果是导航动作，清空答案
            result["answer"] = ""
        
        logger.debug(f"Parsed navigation output: {result}")
        
    except Exception as e:
        logger.warning(f"Failed to parse navigation output: {e}")
    
    return result


def batch_inference(
    predictor,
    batch_data: List[Dict[str, Any]],
    batch_size: int = 4,
    **kwargs
) -> List[Dict[str, Any]]:
    """
    批量推理
    
    Args:
        predictor: 预测器
        batch_data: 批量数据
        batch_size: 批次大小
        **kwargs: 其他参数
        
    Returns:
        List[Dict[str, Any]]: 批量结果
    """
    results = []
    
    for i in range(0, len(batch_data), batch_size):
        batch = batch_data[i:i + batch_size]
        
        logger.info(f"Processing batch {i//batch_size + 1}/{(len(batch_data) + batch_size - 1)//batch_size}")
        
        batch_results = []
        for item in batch:
            try:
                result = predictor.predict(
                    image=item["image"],
                    question=item["question"],
                    **kwargs
                )
                batch_results.append(result)
            except Exception as e:
                logger.error(f"Failed to process item: {e}")
                batch_results.append({
                    "error": str(e),
                    "raw_output": "",
                    "is_navigation": False,
                    "action": None,
                    "answer": ""
                })
        
        results.extend(batch_results)
    
    return results


def validate_inference_input(
    image: Union[str, Image.Image],
    question: str
) -> bool:
    """
    验证推理输入
    
    Args:
        image: 图像
        question: 问题
        
    Returns:
        bool: 是否有效
    """
    try:
        # 验证图像
        if isinstance(image, str):
            import os
            if not os.path.exists(image):
                logger.error(f"Image file not found: {image}")
                return False
        elif isinstance(image, Image.Image):
            if image.size == (0, 0):
                logger.error("Invalid image size")
                return False
        else:
            logger.error(f"Unsupported image type: {type(image)}")
            return False
        
        # 验证问题
        if not isinstance(question, str) or len(question.strip()) == 0:
            logger.error("Question must be a non-empty string")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Input validation failed: {e}")
        return False


def format_inference_result(
    result: Dict[str, Any],
    include_raw: bool = False
) -> Dict[str, Any]:
    """
    格式化推理结果
    
    Args:
        result: 原始结果
        include_raw: 是否包含原始输出
        
    Returns:
        Dict[str, Any]: 格式化结果
    """
    formatted = {
        "is_navigation": result.get("is_navigation", False),
    }
    
    if result.get("is_navigation", False):
        formatted["action"] = result.get("action")
        formatted["response_type"] = "navigation_action"
    else:
        formatted["answer"] = result.get("answer", "")
        formatted["response_type"] = "text_answer"
    
    if include_raw:
        formatted["raw_output"] = result.get("raw_output", "")
    
    return formatted


def create_inference_demo(
    model_path: str,
    image_path: str,
    question: str,
    **kwargs
) -> Dict[str, Any]:
    """
    创建推理演示
    
    Args:
        model_path: 模型路径
        image_path: 图像路径
        question: 问题
        **kwargs: 其他参数
        
    Returns:
        Dict[str, Any]: 演示结果
    """
    try:
        from .predictor import create_predictor
        
        # 创建预测器
        predictor = create_predictor(model_path)
        
        # 执行预测
        result = predictor.predict(
            image=image_path,
            question=question,
            **kwargs
        )
        
        # 格式化结果
        demo_result = {
            "model_path": model_path,
            "image_path": image_path,
            "question": question,
            "prediction": format_inference_result(result, include_raw=True),
            "model_info": predictor.get_model_info(),
            "memory_usage": predictor.get_memory_usage(),
        }
        
        # 清理资源
        predictor.unload()
        
        return demo_result
        
    except Exception as e:
        raise InferenceError(f"Demo creation failed: {e}", cause=e)
