"""
推理预测器模块

定义导航任务的推理预测器
"""

import torch
from typing import Dict, Any, Optional, Union, List
from PIL import Image
import logging

from ..core import get_logger, InferenceError, ExceptionContext
from ..core.constants import (
    NAVIGATION_TOKENS,
    TOKEN_ACTION_MAP,
    DEFAULT_MAX_NEW_TOKENS,
    DEFAULT_TEMPERATURE
)
from ..models import create_model, NavigationTokenizer
from .utils import prepare_inference_input, parse_navigation_output

logger = get_logger(__name__)


class NavigationPredictor:
    """导航任务预测器"""
    
    def __init__(
        self,
        model_path: str,
        device: Optional[str] = None,
        torch_dtype: str = "bfloat16",
        load_in_4bit: bool = False,
        trust_remote_code: bool = True
    ):
        """
        初始化预测器
        
        Args:
            model_path: 模型路径
            device: 设备
            torch_dtype: 数据类型
            load_in_4bit: 是否使用4bit量化
            trust_remote_code: 是否信任远程代码
        """
        self.model_path = model_path
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.torch_dtype = torch_dtype
        self.load_in_4bit = load_in_4bit
        self.trust_remote_code = trust_remote_code
        
        # 模型组件
        self.model = None
        self.tokenizer = None
        self.processor = None
        self.nav_tokenizer = None
        
        # 状态
        self._is_loaded = False
        
        # 加载模型
        self._load_model()
    
    def _load_model(self):
        """加载模型和相关组件"""
        with ExceptionContext(f"Loading inference model: {self.model_path}"):
            try:
                # 创建模型
                model_wrapper = create_model(
                    model_name=self.model_path,
                    device_map=self.device,
                    torch_dtype=self.torch_dtype,
                    trust_remote_code=self.trust_remote_code
                )
                
                # 加载所有组件
                model_wrapper.load_all()
                
                self.model = model_wrapper.get_model()
                self.tokenizer = model_wrapper.get_tokenizer()
                self.processor = model_wrapper.get_processor()
                
                # 准备推理模式
                model_wrapper.prepare_for_inference()
                
                # 创建导航分词器
                self.nav_tokenizer = NavigationTokenizer(self.model_path)
                
                # 调整模型embedding（如果需要）
                self.nav_tokenizer.resize_model_embeddings(self.model)
                
                self._is_loaded = True
                logger.info(f"Inference model loaded successfully: {self.model_path}")
                
            except Exception as e:
                raise InferenceError(
                    f"Failed to load inference model: {e}",
                    input_data=self.model_path,
                    cause=e
                )
    
    def predict(
        self,
        image: Union[str, Image.Image],
        question: str,
        max_new_tokens: int = DEFAULT_MAX_NEW_TOKENS,
        temperature: float = DEFAULT_TEMPERATURE,
        do_sample: bool = True,
        top_p: float = 0.9,
        top_k: int = 50,
        **kwargs
    ) -> Dict[str, Any]:
        """
        进行预测
        
        Args:
            image: 图像路径或PIL图像
            question: 问题文本
            max_new_tokens: 最大新token数
            temperature: 温度参数
            do_sample: 是否采样
            top_p: top-p采样参数
            top_k: top-k采样参数
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 预测结果
        """
        if not self._is_loaded:
            raise InferenceError("Model not loaded")
        
        with ExceptionContext("Model prediction"):
            try:
                # 准备输入
                inputs = prepare_inference_input(
                    image=image,
                    question=question,
                    processor=self.processor,
                    device=self.device
                )
                
                # 生成参数
                generation_kwargs = {
                    "max_new_tokens": max_new_tokens,
                    "temperature": temperature,
                    "do_sample": do_sample,
                    "top_p": top_p,
                    "top_k": top_k,
                    "pad_token_id": self.tokenizer.eos_token_id,
                    **kwargs
                }
                
                # 执行推理
                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        **generation_kwargs
                    )
                
                # 解码输出
                generated_ids = outputs[0][inputs["input_ids"].shape[1]:]
                raw_output = self.tokenizer.decode(generated_ids, skip_special_tokens=True)
                
                # 解析导航输出
                parsed_result = parse_navigation_output(
                    raw_output=raw_output,
                    nav_tokenizer=self.nav_tokenizer
                )
                
                result = {
                    "raw_output": raw_output,
                    "generated_ids": generated_ids.tolist(),
                    **parsed_result
                }
                
                logger.debug(f"Prediction completed: {result}")
                return result
                
            except Exception as e:
                raise InferenceError(
                    f"Prediction failed: {e}",
                    input_data=f"image={type(image).__name__}, question={question[:50]}...",
                    cause=e
                )
    
    def batch_predict(
        self,
        batch_data: List[Dict[str, Any]],
        batch_size: int = 4,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            batch_data: 批量数据，每个元素包含image和question
            batch_size: 批次大小
            **kwargs: 其他预测参数
            
        Returns:
            List[Dict[str, Any]]: 批量预测结果
        """
        results = []
        
        for i in range(0, len(batch_data), batch_size):
            batch = batch_data[i:i + batch_size]
            
            for item in batch:
                try:
                    result = self.predict(
                        image=item["image"],
                        question=item["question"],
                        **kwargs
                    )
                    results.append(result)
                except Exception as e:
                    logger.error(f"Failed to predict item {i}: {e}")
                    results.append({
                        "error": str(e),
                        "raw_output": "",
                        "is_navigation": False,
                        "action": None,
                        "answer": ""
                    })
        
        return results
    
    def predict_action_only(
        self,
        image: Union[str, Image.Image],
        question: str,
        **kwargs
    ) -> Optional[str]:
        """
        仅预测导航动作
        
        Args:
            image: 图像
            question: 问题
            **kwargs: 其他参数
            
        Returns:
            Optional[str]: 导航动作或None
        """
        result = self.predict(image, question, **kwargs)
        
        if result.get("is_navigation", False):
            return result.get("action")
        else:
            return None
    
    def is_navigation_response(self, text: str) -> bool:
        """
        判断响应是否为导航动作
        
        Args:
            text: 响应文本
            
        Returns:
            bool: 是否为导航动作
        """
        for token in NAVIGATION_TOKENS:
            if token in text:
                return True
        return False
    
    def extract_navigation_action(self, text: str) -> Optional[str]:
        """
        从文本中提取导航动作
        
        Args:
            text: 文本
            
        Returns:
            Optional[str]: 导航动作
        """
        for token in NAVIGATION_TOKENS:
            if token in text:
                return TOKEN_ACTION_MAP.get(token)
        return None
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "model_path": self.model_path,
            "device": self.device,
            "torch_dtype": self.torch_dtype,
            "is_loaded": self._is_loaded,
        }
        
        if self._is_loaded and self.model:
            try:
                info.update({
                    "model_type": type(self.model).__name__,
                    "vocab_size": getattr(self.model.config, "vocab_size", None),
                    "max_position_embeddings": getattr(self.model.config, "max_position_embeddings", None),
                })
            except Exception as e:
                logger.warning(f"Error getting model info: {e}")
        
        return info
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        memory_info = {}
        
        if torch.cuda.is_available():
            memory_info["gpu_memory_allocated"] = torch.cuda.memory_allocated() / 1024**3  # GB
            memory_info["gpu_memory_cached"] = torch.cuda.memory_reserved() / 1024**3  # GB
            memory_info["gpu_memory_total"] = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        
        return memory_info
    
    def clear_cache(self):
        """清理缓存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        logger.info("Cache cleared")
    
    def unload(self):
        """卸载模型"""
        if self.model:
            del self.model
            self.model = None
        
        if self.tokenizer:
            del self.tokenizer
            self.tokenizer = None
        
        if self.processor:
            del self.processor
            self.processor = None
        
        if self.nav_tokenizer:
            del self.nav_tokenizer
            self.nav_tokenizer = None
        
        self._is_loaded = False
        self.clear_cache()
        
        logger.info("Model unloaded")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.unload()


def create_predictor(
    model_path: str,
    **kwargs
) -> NavigationPredictor:
    """
    创建预测器的便捷函数
    
    Args:
        model_path: 模型路径
        **kwargs: 其他参数
        
    Returns:
        NavigationPredictor: 预测器实例
    """
    return NavigationPredictor(model_path=model_path, **kwargs)
