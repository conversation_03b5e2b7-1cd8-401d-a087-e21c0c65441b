"""
验证工具模块

提供各种验证功能
"""

import os
import importlib
from typing import Dict, Any, List, Optional, Union
from PIL import Image
import logging

from ..core import get_logger, ValidationError, ExceptionContext
from ..core.constants import SUPPORTED_IMAGE_FORMATS, FILE_EXTENSIONS

logger = get_logger(__name__)


def validate_model_path(model_path: str) -> bool:
    """
    验证模型路径
    
    Args:
        model_path: 模型路径
        
    Returns:
        bool: 是否有效
    """
    try:
        if not os.path.exists(model_path):
            logger.error(f"Model path does not exist: {model_path}")
            return False
        
        # 检查是否为目录
        if not os.path.isdir(model_path):
            logger.error(f"Model path is not a directory: {model_path}")
            return False
        
        # 检查必需的模型文件
        required_files = ["config.json"]
        optional_files = ["pytorch_model.bin", "model.safetensors", "tokenizer.json"]
        
        # 检查必需文件
        for file_name in required_files:
            file_path = os.path.join(model_path, file_name)
            if not os.path.exists(file_path):
                logger.error(f"Required model file not found: {file_path}")
                return False
        
        # 检查至少有一个模型权重文件
        has_weights = False
        for file_name in optional_files:
            file_path = os.path.join(model_path, file_name)
            if os.path.exists(file_path):
                has_weights = True
                break
        
        if not has_weights:
            logger.error(f"No model weight files found in: {model_path}")
            return False
        
        logger.debug(f"Model path validation passed: {model_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error validating model path {model_path}: {e}")
        return False


def validate_data_path(data_path: str, check_structure: bool = True) -> bool:
    """
    验证数据路径
    
    Args:
        data_path: 数据路径
        check_structure: 是否检查数据结构
        
    Returns:
        bool: 是否有效
    """
    try:
        if not os.path.exists(data_path):
            logger.error(f"Data path does not exist: {data_path}")
            return False
        
        if not os.path.isdir(data_path):
            logger.error(f"Data path is not a directory: {data_path}")
            return False
        
        if check_structure:
            # 检查数据集结构
            expected_dirs = ["HM3D", "Matterport"]
            has_data_dirs = False
            
            for dir_name in expected_dirs:
                dir_path = os.path.join(data_path, dir_name)
                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                    has_data_dirs = True
                    break
            
            if not has_data_dirs:
                logger.warning(f"No expected data directories found in: {data_path}")
                # 不返回False，因为可能是其他格式的数据
        
        logger.debug(f"Data path validation passed: {data_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error validating data path {data_path}: {e}")
        return False


def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证配置
    
    Args:
        config: 配置字典
        
    Returns:
        bool: 是否有效
    """
    try:
        errors = []
        
        # 检查必需的配置项
        required_keys = ["model_name", "data_root", "output_dir"]
        
        for key in required_keys:
            if key not in config:
                errors.append(f"Missing required config key: {key}")
            elif not config[key]:
                errors.append(f"Empty config value for key: {key}")
        
        # 验证数值配置
        numeric_configs = {
            "learning_rate": (0, float('inf')),
            "num_train_epochs": (0, float('inf')),
            "per_device_train_batch_size": (1, 1000),
            "lora_rank": (1, 1000),
            "lora_alpha": (1, 1000),
        }
        
        for key, (min_val, max_val) in numeric_configs.items():
            if key in config:
                value = config[key]
                if not isinstance(value, (int, float)):
                    errors.append(f"Config {key} must be numeric, got {type(value)}")
                elif not (min_val <= value <= max_val):
                    errors.append(f"Config {key} must be between {min_val} and {max_val}, got {value}")
        
        # 验证路径配置
        path_configs = ["model_name", "data_root", "split_file"]
        
        for key in path_configs:
            if key in config and config[key]:
                path = config[key]
                if key == "model_name":
                    if not validate_model_path(path):
                        errors.append(f"Invalid model path: {path}")
                elif key == "data_root":
                    if not validate_data_path(path):
                        errors.append(f"Invalid data path: {path}")
                elif key == "split_file":
                    if not os.path.exists(path):
                        errors.append(f"Split file not found: {path}")
        
        if errors:
            for error in errors:
                logger.error(f"Config validation error: {error}")
            return False
        
        logger.debug("Config validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Error validating config: {e}")
        return False


def check_dependencies(required_packages: Optional[List[str]] = None) -> Dict[str, bool]:
    """
    检查依赖包
    
    Args:
        required_packages: 必需的包列表
        
    Returns:
        Dict[str, bool]: 包可用性状态
    """
    if required_packages is None:
        required_packages = [
            "torch",
            "transformers", 
            "peft",
            "accelerate",
            "datasets",
            "PIL",
            "numpy",
            "tqdm"
        ]
    
    package_status = {}
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            package_status[package] = True
            logger.debug(f"Package {package} is available")
        except ImportError:
            package_status[package] = False
            logger.warning(f"Package {package} is not available")
    
    return package_status


def validate_image_file(image_path: str) -> bool:
    """
    验证图像文件
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        bool: 是否有效
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            logger.error(f"Image file does not exist: {image_path}")
            return False
        
        # 检查文件扩展名
        _, ext = os.path.splitext(image_path)
        if ext.lower() not in SUPPORTED_IMAGE_FORMATS:
            logger.error(f"Unsupported image format: {ext}")
            return False
        
        # 尝试加载图像
        try:
            with Image.open(image_path) as img:
                # 检查图像尺寸
                if img.size == (0, 0):
                    logger.error(f"Invalid image size: {image_path}")
                    return False
                
                # 检查图像模式
                if img.mode not in ["RGB", "RGBA", "L"]:
                    logger.warning(f"Unusual image mode {img.mode}: {image_path}")
        
        except Exception as e:
            logger.error(f"Cannot load image {image_path}: {e}")
            return False
        
        logger.debug(f"Image validation passed: {image_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error validating image {image_path}: {e}")
        return False


def validate_json_file(json_path: str, required_keys: Optional[List[str]] = None) -> bool:
    """
    验证JSON文件
    
    Args:
        json_path: JSON文件路径
        required_keys: 必需的键列表
        
    Returns:
        bool: 是否有效
    """
    try:
        if not os.path.exists(json_path):
            logger.error(f"JSON file does not exist: {json_path}")
            return False
        
        # 检查文件扩展名
        if not json_path.lower().endswith('.json'):
            logger.error(f"File is not a JSON file: {json_path}")
            return False
        
        # 尝试加载JSON
        try:
            import json
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format in {json_path}: {e}")
            return False
        
        # 检查必需的键
        if required_keys:
            if isinstance(data, dict):
                missing_keys = [key for key in required_keys if key not in data]
                if missing_keys:
                    logger.error(f"Missing required keys in {json_path}: {missing_keys}")
                    return False
            else:
                logger.error(f"JSON file should contain a dictionary: {json_path}")
                return False
        
        logger.debug(f"JSON validation passed: {json_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error validating JSON file {json_path}: {e}")
        return False


def validate_training_data(data_samples: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    验证训练数据
    
    Args:
        data_samples: 训练样本列表
        
    Returns:
        Dict[str, Any]: 验证结果
    """
    validation_result = {
        "total_samples": len(data_samples),
        "valid_samples": 0,
        "invalid_samples": 0,
        "errors": [],
        "warnings": []
    }
    
    required_fields = ["image_path", "question", "target"]
    
    for i, sample in enumerate(data_samples):
        sample_valid = True
        
        # 检查必需字段
        for field in required_fields:
            if field not in sample:
                validation_result["errors"].append(f"Sample {i}: Missing field '{field}'")
                sample_valid = False
        
        # 验证图像路径
        if "image_path" in sample:
            if not validate_image_file(sample["image_path"]):
                validation_result["errors"].append(f"Sample {i}: Invalid image file")
                sample_valid = False
        
        # 验证文本字段
        text_fields = ["question", "target", "answer"]
        for field in text_fields:
            if field in sample:
                if not isinstance(sample[field], str):
                    validation_result["errors"].append(f"Sample {i}: Field '{field}' must be string")
                    sample_valid = False
                elif len(sample[field].strip()) == 0:
                    validation_result["warnings"].append(f"Sample {i}: Empty field '{field}'")
        
        if sample_valid:
            validation_result["valid_samples"] += 1
        else:
            validation_result["invalid_samples"] += 1
    
    # 计算统计信息
    if validation_result["total_samples"] > 0:
        validation_result["valid_ratio"] = validation_result["valid_samples"] / validation_result["total_samples"]
    else:
        validation_result["valid_ratio"] = 0
    
    return validation_result


def validate_system_requirements() -> Dict[str, Any]:
    """
    验证系统要求
    
    Returns:
        Dict[str, Any]: 系统验证结果
    """
    import torch
    import psutil
    
    requirements = {
        "python_version": True,
        "memory_sufficient": True,
        "disk_space_sufficient": True,
        "gpu_available": torch.cuda.is_available(),
        "dependencies_available": True,
        "warnings": [],
        "errors": []
    }
    
    try:
        # 检查Python版本
        import sys
        python_version = sys.version_info
        if python_version < (3, 8):
            requirements["python_version"] = False
            requirements["errors"].append(f"Python 3.8+ required, got {python_version}")
        
        # 检查内存
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        if memory_gb < 8:
            requirements["memory_sufficient"] = False
            requirements["warnings"].append(f"Low system memory: {memory_gb:.1f}GB")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        free_space_gb = disk.free / (1024**3)
        if free_space_gb < 10:
            requirements["disk_space_sufficient"] = False
            requirements["warnings"].append(f"Low disk space: {free_space_gb:.1f}GB")
        
        # 检查依赖包
        package_status = check_dependencies()
        missing_packages = [pkg for pkg, available in package_status.items() if not available]
        if missing_packages:
            requirements["dependencies_available"] = False
            requirements["errors"].append(f"Missing packages: {missing_packages}")
        
        # GPU检查
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            requirements["gpu_count"] = gpu_count
            requirements["gpu_memory"] = []
            
            for i in range(gpu_count):
                props = torch.cuda.get_device_properties(i)
                gpu_memory_gb = props.total_memory / (1024**3)
                requirements["gpu_memory"].append(gpu_memory_gb)
                
                if gpu_memory_gb < 4:
                    requirements["warnings"].append(f"GPU {i} has low memory: {gpu_memory_gb:.1f}GB")
        
    except Exception as e:
        requirements["errors"].append(f"Error checking system requirements: {e}")
    
    return requirements
