"""
设备管理工具模块

提供设备检测、GPU管理等功能
"""

import torch
import os
from typing import Dict, Any, Optional, List
import logging

from ..core import get_logger

logger = get_logger(__name__)


def get_device_info() -> Dict[str, Any]:
    """
    获取设备信息
    
    Returns:
        Dict[str, Any]: 设备信息
    """
    device_info = {
        "cpu_count": os.cpu_count(),
        "cuda_available": torch.cuda.is_available(),
        "mps_available": hasattr(torch.backends, 'mps') and torch.backends.mps.is_available(),
    }
    
    # CUDA信息
    if torch.cuda.is_available():
        device_info.update({
            "cuda_device_count": torch.cuda.device_count(),
            "cuda_current_device": torch.cuda.current_device(),
            "cuda_devices": []
        })
        
        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            device_info["cuda_devices"].append({
                "device_id": i,
                "name": device_props.name,
                "total_memory": device_props.total_memory / 1024**3,  # GB
                "major": device_props.major,
                "minor": device_props.minor,
                "multi_processor_count": device_props.multi_processor_count,
            })
    
    # PyTorch版本信息
    device_info["torch_version"] = torch.__version__
    device_info["cuda_version"] = torch.version.cuda if torch.cuda.is_available() else None
    
    return device_info


def get_optimal_device() -> str:
    """
    获取最优设备
    
    Returns:
        str: 设备名称
    """
    if torch.cuda.is_available():
        # 选择内存最大的GPU
        max_memory = 0
        best_device = 0
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            if props.total_memory > max_memory:
                max_memory = props.total_memory
                best_device = i
        
        device = f"cuda:{best_device}"
        logger.info(f"Selected optimal device: {device}")
        return device
    
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        logger.info("Selected device: mps")
        return "mps"
    
    else:
        logger.info("Selected device: cpu")
        return "cpu"


def check_gpu_memory(device_id: int = 0) -> Dict[str, float]:
    """
    检查GPU内存使用情况
    
    Args:
        device_id: GPU设备ID
        
    Returns:
        Dict[str, float]: 内存信息（GB）
    """
    if not torch.cuda.is_available():
        return {}
    
    try:
        torch.cuda.set_device(device_id)
        
        total_memory = torch.cuda.get_device_properties(device_id).total_memory / 1024**3
        allocated_memory = torch.cuda.memory_allocated(device_id) / 1024**3
        cached_memory = torch.cuda.memory_reserved(device_id) / 1024**3
        free_memory = total_memory - allocated_memory
        
        return {
            "total_memory": total_memory,
            "allocated_memory": allocated_memory,
            "cached_memory": cached_memory,
            "free_memory": free_memory,
            "utilization": (allocated_memory / total_memory) * 100 if total_memory > 0 else 0,
        }
        
    except Exception as e:
        logger.warning(f"Failed to check GPU memory for device {device_id}: {e}")
        return {}


def get_all_gpu_memory() -> List[Dict[str, float]]:
    """
    获取所有GPU的内存信息
    
    Returns:
        List[Dict[str, float]]: 所有GPU的内存信息
    """
    if not torch.cuda.is_available():
        return []
    
    gpu_memory_info = []
    
    for i in range(torch.cuda.device_count()):
        memory_info = check_gpu_memory(i)
        if memory_info:
            memory_info["device_id"] = i
            gpu_memory_info.append(memory_info)
    
    return gpu_memory_info


def clear_gpu_cache(device_id: Optional[int] = None):
    """
    清理GPU缓存
    
    Args:
        device_id: GPU设备ID，None表示清理所有设备
    """
    if not torch.cuda.is_available():
        logger.warning("CUDA not available, cannot clear GPU cache")
        return
    
    try:
        if device_id is not None:
            torch.cuda.set_device(device_id)
            torch.cuda.empty_cache()
            logger.info(f"GPU cache cleared for device {device_id}")
        else:
            torch.cuda.empty_cache()
            logger.info("GPU cache cleared for all devices")
            
    except Exception as e:
        logger.warning(f"Failed to clear GPU cache: {e}")


def set_device_memory_fraction(fraction: float, device_id: int = 0):
    """
    设置GPU内存使用比例
    
    Args:
        fraction: 内存使用比例 (0.0 - 1.0)
        device_id: GPU设备ID
    """
    if not torch.cuda.is_available():
        logger.warning("CUDA not available, cannot set memory fraction")
        return
    
    try:
        torch.cuda.set_per_process_memory_fraction(fraction, device_id)
        logger.info(f"Set memory fraction to {fraction} for device {device_id}")
        
    except Exception as e:
        logger.warning(f"Failed to set memory fraction: {e}")


def get_gpu_utilization() -> List[Dict[str, Any]]:
    """
    获取GPU利用率信息
    
    Returns:
        List[Dict[str, Any]]: GPU利用率信息
    """
    if not torch.cuda.is_available():
        return []
    
    utilization_info = []
    
    try:
        import pynvml
        pynvml.nvmlInit()
        
        for i in range(torch.cuda.device_count()):
            handle = pynvml.nvmlDeviceGetHandleByIndex(i)
            
            # 获取利用率
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            
            # 获取温度
            temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
            
            # 获取功耗
            power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为瓦特
            
            utilization_info.append({
                "device_id": i,
                "gpu_utilization": util.gpu,
                "memory_utilization": util.memory,
                "temperature": temp,
                "power_usage": power,
            })
        
        pynvml.nvmlShutdown()
        
    except ImportError:
        logger.warning("pynvml not available, cannot get detailed GPU utilization")
    except Exception as e:
        logger.warning(f"Failed to get GPU utilization: {e}")
    
    return utilization_info


def monitor_gpu_memory(interval: int = 5, duration: int = 60):
    """
    监控GPU内存使用情况
    
    Args:
        interval: 监控间隔（秒）
        duration: 监控持续时间（秒）
    """
    if not torch.cuda.is_available():
        logger.warning("CUDA not available, cannot monitor GPU memory")
        return
    
    import time
    
    logger.info(f"Starting GPU memory monitoring for {duration} seconds...")
    
    start_time = time.time()
    
    while time.time() - start_time < duration:
        memory_info = get_all_gpu_memory()
        
        for info in memory_info:
            logger.info(
                f"GPU {info['device_id']}: "
                f"{info['allocated_memory']:.1f}GB / {info['total_memory']:.1f}GB "
                f"({info['utilization']:.1f}%)"
            )
        
        time.sleep(interval)
    
    logger.info("GPU memory monitoring completed")


def select_best_gpu() -> Optional[int]:
    """
    选择最佳GPU设备
    
    Returns:
        Optional[int]: 最佳GPU设备ID，如果没有可用GPU则返回None
    """
    if not torch.cuda.is_available():
        return None
    
    memory_info = get_all_gpu_memory()
    
    if not memory_info:
        return None
    
    # 选择空闲内存最多的GPU
    best_gpu = max(memory_info, key=lambda x: x['free_memory'])
    
    logger.info(f"Selected best GPU: {best_gpu['device_id']} "
                f"(free memory: {best_gpu['free_memory']:.1f}GB)")
    
    return best_gpu['device_id']


def check_device_compatibility(device: str) -> bool:
    """
    检查设备兼容性
    
    Args:
        device: 设备名称
        
    Returns:
        bool: 是否兼容
    """
    try:
        if device == "cpu":
            return True
        elif device.startswith("cuda"):
            if not torch.cuda.is_available():
                return False
            
            device_id = int(device.split(":")[1]) if ":" in device else 0
            return device_id < torch.cuda.device_count()
        
        elif device == "mps":
            return hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
        
        else:
            return False
            
    except Exception as e:
        logger.warning(f"Error checking device compatibility for {device}: {e}")
        return False


def optimize_device_settings():
    """优化设备设置"""
    try:
        if torch.cuda.is_available():
            # 启用TensorFloat-32 (TF32)
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            
            # 启用cuDNN基准测试
            torch.backends.cudnn.benchmark = True
            
            # 设置cuDNN确定性（如果需要）
            # torch.backends.cudnn.deterministic = True
            
            logger.info("CUDA device settings optimized")
        
        # 设置线程数
        if hasattr(torch, 'set_num_threads'):
            num_threads = min(os.cpu_count(), 8)  # 限制最大线程数
            torch.set_num_threads(num_threads)
            logger.info(f"Set PyTorch threads to: {num_threads}")
            
    except Exception as e:
        logger.warning(f"Failed to optimize device settings: {e}")


def get_device_recommendations() -> Dict[str, Any]:
    """
    获取设备使用建议
    
    Returns:
        Dict[str, Any]: 设备建议
    """
    recommendations = {
        "optimal_device": get_optimal_device(),
        "device_info": get_device_info(),
    }
    
    if torch.cuda.is_available():
        memory_info = get_all_gpu_memory()
        recommendations["gpu_memory"] = memory_info
        
        # 内存建议
        if memory_info:
            max_memory = max(info['total_memory'] for info in memory_info)
            
            if max_memory < 8:
                recommendations["batch_size_suggestion"] = 1
                recommendations["memory_warning"] = "Low GPU memory, consider using smaller batch size"
            elif max_memory < 16:
                recommendations["batch_size_suggestion"] = 2
            else:
                recommendations["batch_size_suggestion"] = 4
    
    return recommendations
