"""
文件操作工具模块

提供安全的文件操作功能
"""

import os
import json
import shutil
import glob
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

from ..core import get_logger, DataProcessingError, ExceptionContext

logger = get_logger(__name__)


def ensure_dir(path: str) -> str:
    """
    确保目录存在
    
    Args:
        path: 目录路径
        
    Returns:
        str: 目录路径
    """
    try:
        os.makedirs(path, exist_ok=True)
        return path
    except Exception as e:
        raise DataProcessingError(f"Failed to create directory: {path}", cause=e)


def safe_save_json(data: Dict[str, Any], file_path: str, indent: int = 2):
    """
    安全保存JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
        indent: 缩进
    """
    with ExceptionContext(f"Saving JSON file: {file_path}"):
        # 确保目录存在
        ensure_dir(os.path.dirname(file_path))
        
        # 先保存到临时文件
        temp_path = f"{file_path}.tmp"
        
        try:
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False, default=str)
            
            # 原子性移动
            shutil.move(temp_path, file_path)
            logger.debug(f"JSON file saved: {file_path}")
            
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise DataProcessingError(f"Failed to save JSON file: {file_path}", cause=e)


def safe_load_json(file_path: str) -> Dict[str, Any]:
    """
    安全加载JSON文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        Dict[str, Any]: JSON数据
    """
    with ExceptionContext(f"Loading JSON file: {file_path}"):
        if not os.path.exists(file_path):
            raise DataProcessingError(f"JSON file not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.debug(f"JSON file loaded: {file_path}")
            return data
            
        except json.JSONDecodeError as e:
            raise DataProcessingError(f"Invalid JSON format in file: {file_path}", cause=e)
        except Exception as e:
            raise DataProcessingError(f"Failed to load JSON file: {file_path}", cause=e)


def get_file_size(file_path: str) -> int:
    """
    获取文件大小
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 文件大小（字节）
    """
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        logger.warning(f"Failed to get file size: {file_path}, error: {e}")
        return 0


def get_file_size_mb(file_path: str) -> float:
    """
    获取文件大小（MB）
    
    Args:
        file_path: 文件路径
        
    Returns:
        float: 文件大小（MB）
    """
    return get_file_size(file_path) / (1024 * 1024)


def copy_file_with_progress(src: str, dst: str, chunk_size: int = 1024*1024):
    """
    带进度的文件复制
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        chunk_size: 块大小
    """
    with ExceptionContext(f"Copying file: {src} -> {dst}"):
        if not os.path.exists(src):
            raise DataProcessingError(f"Source file not found: {src}")
        
        # 确保目标目录存在
        ensure_dir(os.path.dirname(dst))
        
        try:
            file_size = get_file_size(src)
            copied = 0
            
            with open(src, 'rb') as src_file, open(dst, 'wb') as dst_file:
                while True:
                    chunk = src_file.read(chunk_size)
                    if not chunk:
                        break
                    
                    dst_file.write(chunk)
                    copied += len(chunk)
                    
                    # 记录进度
                    if file_size > 0:
                        progress = (copied / file_size) * 100
                        if copied % (chunk_size * 10) == 0:  # 每10个块记录一次
                            logger.debug(f"Copy progress: {progress:.1f}%")
            
            logger.info(f"File copied successfully: {src} -> {dst}")
            
        except Exception as e:
            # 清理不完整的目标文件
            if os.path.exists(dst):
                os.remove(dst)
            raise DataProcessingError(f"Failed to copy file: {src} -> {dst}", cause=e)


def find_files_by_pattern(
    directory: str,
    pattern: str,
    recursive: bool = True
) -> List[str]:
    """
    按模式查找文件
    
    Args:
        directory: 搜索目录
        pattern: 文件模式（如*.json）
        recursive: 是否递归搜索
        
    Returns:
        List[str]: 匹配的文件路径列表
    """
    try:
        if recursive:
            search_pattern = os.path.join(directory, "**", pattern)
            files = glob.glob(search_pattern, recursive=True)
        else:
            search_pattern = os.path.join(directory, pattern)
            files = glob.glob(search_pattern)
        
        # 返回绝对路径
        return [os.path.abspath(f) for f in files]
        
    except Exception as e:
        logger.warning(f"Failed to find files with pattern {pattern} in {directory}: {e}")
        return []


def find_files_by_extension(
    directory: str,
    extensions: List[str],
    recursive: bool = True
) -> List[str]:
    """
    按扩展名查找文件
    
    Args:
        directory: 搜索目录
        extensions: 扩展名列表（如['.json', '.txt']）
        recursive: 是否递归搜索
        
    Returns:
        List[str]: 匹配的文件路径列表
    """
    all_files = []
    
    for ext in extensions:
        if not ext.startswith('.'):
            ext = '.' + ext
        
        pattern = f"*{ext}"
        files = find_files_by_pattern(directory, pattern, recursive)
        all_files.extend(files)
    
    return sorted(list(set(all_files)))  # 去重并排序


def backup_file(file_path: str, backup_suffix: str = ".bak") -> str:
    """
    备份文件
    
    Args:
        file_path: 文件路径
        backup_suffix: 备份后缀
        
    Returns:
        str: 备份文件路径
    """
    if not os.path.exists(file_path):
        raise DataProcessingError(f"File not found for backup: {file_path}")
    
    backup_path = f"{file_path}{backup_suffix}"
    
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"File backed up: {file_path} -> {backup_path}")
        return backup_path
        
    except Exception as e:
        raise DataProcessingError(f"Failed to backup file: {file_path}", cause=e)


def clean_directory(
    directory: str,
    pattern: Optional[str] = None,
    older_than_days: Optional[int] = None
):
    """
    清理目录
    
    Args:
        directory: 目录路径
        pattern: 文件模式（可选）
        older_than_days: 删除多少天前的文件（可选）
    """
    if not os.path.exists(directory):
        logger.warning(f"Directory not found for cleaning: {directory}")
        return
    
    try:
        import time
        
        current_time = time.time()
        deleted_count = 0
        
        if pattern:
            files = find_files_by_pattern(directory, pattern, recursive=True)
        else:
            files = find_files_by_pattern(directory, "*", recursive=True)
        
        for file_path in files:
            try:
                # 检查文件年龄
                if older_than_days:
                    file_age_days = (current_time - os.path.getmtime(file_path)) / (24 * 3600)
                    if file_age_days < older_than_days:
                        continue
                
                os.remove(file_path)
                deleted_count += 1
                logger.debug(f"Deleted file: {file_path}")
                
            except Exception as e:
                logger.warning(f"Failed to delete file {file_path}: {e}")
        
        logger.info(f"Directory cleaned: {directory}, deleted {deleted_count} files")
        
    except Exception as e:
        logger.error(f"Failed to clean directory {directory}: {e}")


def get_directory_size(directory: str) -> int:
    """
    获取目录大小
    
    Args:
        directory: 目录路径
        
    Returns:
        int: 目录大小（字节）
    """
    total_size = 0
    
    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(file_path)
                except (OSError, IOError):
                    continue
        
        return total_size
        
    except Exception as e:
        logger.warning(f"Failed to calculate directory size: {directory}, error: {e}")
        return 0


def get_directory_size_mb(directory: str) -> float:
    """
    获取目录大小（MB）
    
    Args:
        directory: 目录路径
        
    Returns:
        float: 目录大小（MB）
    """
    return get_directory_size(directory) / (1024 * 1024)


def create_symlink(src: str, dst: str):
    """
    创建符号链接
    
    Args:
        src: 源路径
        dst: 目标路径
    """
    try:
        if os.path.exists(dst):
            os.remove(dst)
        
        os.symlink(src, dst)
        logger.info(f"Symlink created: {src} -> {dst}")
        
    except Exception as e:
        logger.warning(f"Failed to create symlink {src} -> {dst}: {e}")


def is_file_locked(file_path: str) -> bool:
    """
    检查文件是否被锁定
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否被锁定
    """
    try:
        with open(file_path, 'a'):
            return False
    except IOError:
        return True
