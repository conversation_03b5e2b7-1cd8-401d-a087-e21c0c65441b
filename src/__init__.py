"""
重构后的导航模型微调项目

采用模块化架构，提供清晰的分层设计和统一的接口
"""

# 核心模块
from .core import (
    Config,
    ConfigManager,
    get_logger,
    setup_logger,
    NavigationError,
    ModelLoadError,
    DataProcessingError,
    TrainingError,
    InferenceError,
    ExceptionContext,
)

# 数据模块
from .data import (
    NavigationDataset,
    NavigationDataLoader,
    NavigationProcessor,
    NavigationCollator,
    create_collate_fn,
)

# 模型模块
from .models import (
    create_model,
    BaseModel,
    ModelFactory,
    QwenVLModel,
    QwenModelConfig,
    LoRAConfig,
    LoRAManager,
    apply_lora_to_model,
    NavigationTokenizer,
    get_navigation_tokenizer,
)

# 训练模块
from .training import (
    NavigationTrainer,
    create_trainer,
    TrainingConfig,
    create_training_arguments,
    setup_callbacks,
    setup_training_environment,
)

# 推理模块
from .inference import NavigationPredictor, create_predictor, load_inference_model

# 工具模块
from .utils import (
    validate_system_requirements,
    get_device_info,
    safe_save_json,
    safe_load_json,
    validate_model_path,
    validate_data_path,
)

__version__ = "2.0.0"
__author__ = "EQACL Team"

__all__ = [
    # 核心
    "Config",
    "ConfigManager",
    "get_logger",
    "setup_logger",
    "NavigationError",
    "ModelLoadError",
    "DataProcessingError",
    "TrainingError",
    "InferenceError",
    "ExceptionContext",
    # 数据
    "NavigationDataset",
    "NavigationDataLoader",
    "NavigationProcessor",
    "NavigationCollator",
    "create_collate_fn",
    # 模型
    "create_model",
    "BaseModel",
    "ModelFactory",
    "QwenVLModel",
    "QwenModelConfig",
    "LoRAConfig",
    "LoRAManager",
    "apply_lora_to_model",
    "NavigationTokenizer",
    "get_navigation_tokenizer",
    # 训练
    "NavigationTrainer",
    "create_trainer",
    "TrainingConfig",
    "create_training_arguments",
    "setup_callbacks",
    "setup_training_environment",
    # 推理
    "NavigationPredictor",
    "create_predictor",
    "load_inference_model",
    # 工具
    "validate_system_requirements",
    "get_device_info",
    "safe_save_json",
    "safe_load_json",
    "validate_model_path",
    "validate_data_path",
]
