"""
自定义异常处理模块

定义项目专用的异常类和错误处理工具
"""

from typing import Optional, Any, Dict
import traceback
import logging

logger = logging.getLogger(__name__)


class NavigationError(Exception):
    """导航任务基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详情
            cause: 原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
    
    def __str__(self) -> str:
        """字符串表示"""
        parts = [self.message]
        
        if self.error_code:
            parts.append(f"Error Code: {self.error_code}")
        
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            parts.append(f"Details: {details_str}")
        
        if self.cause:
            parts.append(f"Caused by: {self.cause}")
        
        return " | ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "cause": str(self.cause) if self.cause else None
        }


class ModelLoadError(NavigationError):
    """模型加载异常"""
    
    def __init__(
        self,
        message: str = "Failed to load model",
        model_path: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if model_path:
            details['model_path'] = model_path
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class DataProcessingError(NavigationError):
    """数据处理异常"""
    
    def __init__(
        self,
        message: str = "Data processing failed",
        data_path: Optional[str] = None,
        sample_index: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if data_path:
            details['data_path'] = data_path
        if sample_index is not None:
            details['sample_index'] = sample_index
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class TrainingError(NavigationError):
    """训练异常"""
    
    def __init__(
        self,
        message: str = "Training failed",
        epoch: Optional[int] = None,
        step: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if epoch is not None:
            details['epoch'] = epoch
        if step is not None:
            details['step'] = step
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class InferenceError(NavigationError):
    """推理异常"""
    
    def __init__(
        self,
        message: str = "Inference failed",
        input_data: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if input_data:
            details['input_data'] = str(input_data)[:100]  # 限制长度
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class ConfigurationError(NavigationError):
    """配置异常"""
    
    def __init__(
        self,
        message: str = "Configuration error",
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = str(config_value)
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class ValidationError(NavigationError):
    """验证异常"""
    
    def __init__(
        self,
        message: str = "Validation failed",
        validation_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if validation_type:
            details['validation_type'] = validation_type
        kwargs['details'] = details
        super().__init__(message, **kwargs)


def handle_exception(
    exception: Exception,
    logger_name: str = "navigation",
    reraise: bool = True
) -> Optional[NavigationError]:
    """
    统一异常处理函数
    
    Args:
        exception: 原始异常
        logger_name: 日志器名称
        reraise: 是否重新抛出异常
        
    Returns:
        NavigationError: 包装后的异常（如果不重新抛出）
    """
    logger = logging.getLogger(logger_name)
    
    # 如果已经是NavigationError，直接处理
    if isinstance(exception, NavigationError):
        logger.error(f"Navigation error: {exception}")
        if reraise:
            raise exception
        return exception
    
    # 包装其他异常
    wrapped_exception = NavigationError(
        message=f"Unexpected error: {str(exception)}",
        error_code="UNEXPECTED_ERROR",
        cause=exception
    )
    
    # 记录详细的堆栈跟踪
    logger.error(f"Unexpected error occurred: {exception}")
    logger.debug(f"Stack trace: {traceback.format_exc()}")
    
    if reraise:
        raise wrapped_exception
    
    return wrapped_exception


def safe_execute(func, *args, **kwargs):
    """
    安全执行函数，自动处理异常
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        执行结果或None（如果出错）
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handle_exception(e, reraise=False)
        return None


class ExceptionContext:
    """异常上下文管理器"""
    
    def __init__(
        self,
        operation_name: str,
        logger_name: str = "navigation",
        reraise: bool = True
    ):
        """
        初始化异常上下文
        
        Args:
            operation_name: 操作名称
            logger_name: 日志器名称
            reraise: 是否重新抛出异常
        """
        self.operation_name = operation_name
        self.logger_name = logger_name
        self.reraise = reraise
        self.logger = logging.getLogger(logger_name)
    
    def __enter__(self):
        """进入上下文"""
        self.logger.debug(f"Starting operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if exc_type is None:
            self.logger.debug(f"Operation completed successfully: {self.operation_name}")
            return False
        
        # 处理异常
        self.logger.error(f"Operation failed: {self.operation_name}")
        handle_exception(exc_val, self.logger_name, self.reraise)
        
        # 如果不重新抛出，返回True抑制异常
        return not self.reraise
