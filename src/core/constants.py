"""
项目常量定义模块

定义项目中使用的常量和枚举值
"""

from enum import Enum
from typing import List, Tuple, Dict, Any

# 导航动作Token
NAVIGATION_TOKENS = ["<move_forward>", "<turn_left>", "<turn_right>"]

# 动作映射
ACTION_TOKEN_MAP = {
    "move_forward": "<move_forward>",
    "turn_left": "<turn_left>",
    "turn_right": "<turn_right>",
}

TOKEN_ACTION_MAP = {v: k for k, v in ACTION_TOKEN_MAP.items()}

# 默认模型配置
DEFAULT_MODEL_NAME = "data/Qwen2.5-VL-3B-Instruct"
DEFAULT_IMAGE_SIZE = (224, 224)
DEFAULT_MAX_LENGTH = 2048

# 支持的图像格式
SUPPORTED_IMAGE_FORMATS = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]

# 数据集相关常量
DATASET_SPLITS = ["train", "val", "test"]
DEFAULT_SPLIT_RATIOS = {"train": 0.8, "val": 0.1, "test": 0.1}

# 训练相关常量
DEFAULT_BATCH_SIZE = 2
DEFAULT_LEARNING_RATE = 2e-4
DEFAULT_NUM_EPOCHS = 1
DEFAULT_WARMUP_RATIO = 0.03
DEFAULT_WEIGHT_DECAY = 0.01

# LoRA配置常量
DEFAULT_LORA_RANK = 16
DEFAULT_LORA_ALPHA = 32
DEFAULT_LORA_DROPOUT = 0.1
DEFAULT_LORA_TARGET_MODULES = [
    "q_proj",
    "k_proj",
    "v_proj",
    "o_proj",
    "gate_proj",
    "up_proj",
    "down_proj",
]

# 推理相关常量
DEFAULT_MAX_NEW_TOKENS = 256
DEFAULT_TEMPERATURE = 0.7
DEFAULT_TOP_P = 0.9
DEFAULT_TOP_K = 50

# 文件路径常量
DEFAULT_DATA_ROOT = "data/finetune"
DEFAULT_SPLIT_FILE = "data/finetune/split.json"
DEFAULT_OUTPUT_DIR = "results"
DEFAULT_CHECKPOINT_DIR = "checkpoints"
DEFAULT_LOG_DIR = "logs"

# 系统配置常量
DEFAULT_DEVICE_MAP = "auto"
DEFAULT_TORCH_DTYPE = "bfloat16"
DEFAULT_TRUST_REMOTE_CODE = True

# 特殊Token
IGNORE_INDEX = -100
PAD_TOKEN = "<pad>"
UNK_TOKEN = "<unk>"
BOS_TOKEN = "<s>"
EOS_TOKEN = "</s>"

# 视觉Token
VISION_START_TOKEN = "<|vision_start|>"
VISION_END_TOKEN = "<|vision_end|>"
IMAGE_PAD_TOKEN = "<|image_pad|>"
VIDEO_PAD_TOKEN = "<|video_pad|>"

# 聊天模板
DEFAULT_CHAT_TEMPLATE = (
    "{% for message in messages %}"
    "{{'<|im_start|>' + message['role'] + '\\n' + message['content'] + '<|im_end|>' + '\\n'}}"
    "{% endfor %}"
    "{% if add_generation_prompt %}"
    "{{ '<|im_start|>assistant\\n' }}"
    "{% endif %}"
)

# 系统消息
DEFAULT_SYSTEM_MESSAGE = (
    "You are a helpful assistant for visual navigation and qa tasks."
)


# 错误代码
class ErrorCode(Enum):
    """错误代码枚举"""

    # 通用错误
    UNKNOWN_ERROR = "E0000"
    CONFIGURATION_ERROR = "E0001"
    VALIDATION_ERROR = "E0002"

    # 模型相关错误
    MODEL_LOAD_ERROR = "E1001"
    MODEL_INIT_ERROR = "E1002"
    MODEL_FORWARD_ERROR = "E1003"
    TOKENIZER_ERROR = "E1004"

    # 数据相关错误
    DATA_LOAD_ERROR = "E2001"
    DATA_PROCESS_ERROR = "E2002"
    DATA_VALIDATION_ERROR = "E2003"
    IMAGE_LOAD_ERROR = "E2004"

    # 训练相关错误
    TRAINING_INIT_ERROR = "E3001"
    TRAINING_STEP_ERROR = "E3002"
    CHECKPOINT_SAVE_ERROR = "E3003"
    CHECKPOINT_LOAD_ERROR = "E3004"

    # 推理相关错误
    INFERENCE_INIT_ERROR = "E4001"
    INFERENCE_PREDICT_ERROR = "E4002"
    OUTPUT_PARSE_ERROR = "E4003"


# 日志级别
class LogLevel(Enum):
    """日志级别枚举"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# 训练状态
class TrainingState(Enum):
    """训练状态枚举"""

    NOT_STARTED = "not_started"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# 模型类型
class ModelType(Enum):
    """模型类型枚举"""

    QWEN_VL = "qwen_vl"
    QWEN2_VL = "qwen2_vl"
    QWEN2_5_VL = "qwen2_5_vl"


# 数据集类型
class DatasetType(Enum):
    """数据集类型枚举"""

    HM3D = "hm3d"
    MATTERPORT = "matterport"
    MIXED = "mixed"


# 任务类型
class TaskType(Enum):
    """任务类型枚举"""

    NAVIGATION = "navigation"
    VQA = "vqa"
    CAPTIONING = "captioning"


# 设备类型
class DeviceType(Enum):
    """设备类型枚举"""

    CPU = "cpu"
    CUDA = "cuda"
    MPS = "mps"
    AUTO = "auto"


# 精度类型
class PrecisionType(Enum):
    """精度类型枚举"""

    FP32 = "float32"
    FP16 = "float16"
    BF16 = "bfloat16"
    INT8 = "int8"
    INT4 = "int4"


# 优化器类型
class OptimizerType(Enum):
    """优化器类型枚举"""

    ADAM = "adam"
    ADAMW = "adamw"
    SGD = "sgd"
    ADAFACTOR = "adafactor"


# 学习率调度器类型
class SchedulerType(Enum):
    """学习率调度器类型枚举"""

    LINEAR = "linear"
    COSINE = "cosine"
    COSINE_WITH_RESTARTS = "cosine_with_restarts"
    POLYNOMIAL = "polynomial"
    CONSTANT = "constant"
    CONSTANT_WITH_WARMUP = "constant_with_warmup"


# 验证指标
VALIDATION_METRICS = [
    "accuracy",
    "precision",
    "recall",
    "f1_score",
    "bleu_score",
    "rouge_score",
]

# 环境变量
ENV_VARS = {
    "CUDA_VISIBLE_DEVICES": "CUDA_VISIBLE_DEVICES",
    "TOKENIZERS_PARALLELISM": "TOKENIZERS_PARALLELISM",
    "TRANSFORMERS_CACHE": "TRANSFORMERS_CACHE",
    "HF_HOME": "HF_HOME",
}

# 默认环境变量值
DEFAULT_ENV_VALUES = {
    "TOKENIZERS_PARALLELISM": "false",
    "TRANSFORMERS_OFFLINE": "0",
}

# 文件扩展名
FILE_EXTENSIONS = {
    "config": [".yaml", ".yml", ".json"],
    "model": [".bin", ".safetensors", ".pt", ".pth"],
    "data": [".json", ".jsonl", ".csv", ".pkl", ".pickle"],
    "log": [".log", ".txt"],
}

# 内存和性能相关常量
MEMORY_EFFICIENT_BATCH_SIZE = 1
GRADIENT_CHECKPOINTING_THRESHOLD = 1000  # MB
MAX_SEQUENCE_LENGTH = 4096
MIN_SEQUENCE_LENGTH = 32

# API相关常量
API_TIMEOUT = 30  # seconds
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
