"""
统一配置管理模块

提供配置文件加载、验证和管理功能
"""

import os
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置数据类"""
    
    # 模型配置
    model_name: str = "data/Qwen2.5-VL-3B-Instruct"
    use_4bit: bool = True
    use_lora: bool = True
    lora_rank: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    target_modules: list = field(default_factory=lambda: [
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ])
    
    # 数据配置
    data_root: str = "data/finetune"
    split_file: str = "data/finetune/split.json"
    max_length: int = 2048
    image_size: tuple = (224, 224)
    
    # 训练配置
    output_dir: str = "results"
    num_train_epochs: int = 1
    per_device_train_batch_size: int = 2
    gradient_accumulation_steps: int = 4
    learning_rate: float = 2e-4
    weight_decay: float = 0.01
    warmup_ratio: float = 0.03
    save_steps: int = 500
    logging_steps: int = 10
    
    # 推理配置
    max_new_tokens: int = 256
    temperature: float = 0.7
    do_sample: bool = True
    
    # 系统配置
    device_map: str = "auto"
    torch_dtype: str = "bfloat16"
    trust_remote_code: bool = True
    
    def __post_init__(self):
        """配置后处理"""
        # 转换路径为绝对路径
        if not os.path.isabs(self.model_name):
            self.model_name = os.path.abspath(self.model_name)
        if not os.path.isabs(self.data_root):
            self.data_root = os.path.abspath(self.data_root)
        if not os.path.isabs(self.split_file):
            self.split_file = os.path.abspath(self.split_file)
        if not os.path.isabs(self.output_dir):
            self.output_dir = os.path.abspath(self.output_dir)
    
    def validate(self) -> bool:
        """验证配置有效性"""
        errors = []
        
        # 验证模型路径
        if not os.path.exists(self.model_name):
            errors.append(f"Model path does not exist: {self.model_name}")
        
        # 验证数据路径
        if not os.path.exists(self.data_root):
            errors.append(f"Data root does not exist: {self.data_root}")
        
        # 验证分割文件
        if not os.path.exists(self.split_file):
            errors.append(f"Split file does not exist: {self.split_file}")
        
        # 验证数值参数
        if self.lora_rank <= 0:
            errors.append("LoRA rank must be positive")
        if self.learning_rate <= 0:
            errors.append("Learning rate must be positive")
        if self.per_device_train_batch_size <= 0:
            errors.append("Batch size must be positive")
        
        if errors:
            for error in errors:
                logger.error(f"Config validation error: {error}")
            return False
        
        return True


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self._config = None
    
    def load_config(self, config_path: Optional[str] = None) -> Config:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Config: 配置对象
        """
        if config_path:
            self.config_path = config_path
        
        if self.config_path and os.path.exists(self.config_path):
            logger.info(f"Loading config from: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            # 创建配置对象
            self._config = Config(**config_dict)
        else:
            logger.info("Using default configuration")
            self._config = Config()
        
        # 验证配置
        if not self._config.validate():
            raise ValueError("Configuration validation failed")
        
        return self._config
    
    def save_config(self, config: Config, save_path: str):
        """
        保存配置到文件
        
        Args:
            config: 配置对象
            save_path: 保存路径
        """
        config_dict = config.__dict__
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"Config saved to: {save_path}")
    
    def get_config(self) -> Config:
        """获取当前配置"""
        if self._config is None:
            self._config = self.load_config()
        return self._config
    
    def update_config(self, **kwargs) -> Config:
        """
        更新配置参数
        
        Args:
            **kwargs: 要更新的配置参数
            
        Returns:
            Config: 更新后的配置对象
        """
        if self._config is None:
            self._config = self.load_config()
        
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
                logger.info(f"Updated config: {key} = {value}")
            else:
                logger.warning(f"Unknown config parameter: {key}")
        
        return self._config


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> Config:
    """获取全局配置"""
    return config_manager.get_config()


def load_config(config_path: str) -> Config:
    """加载配置文件"""
    return config_manager.load_config(config_path)


def update_config(**kwargs) -> Config:
    """更新配置参数"""
    return config_manager.update_config(**kwargs)
