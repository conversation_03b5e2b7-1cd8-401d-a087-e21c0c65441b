"""
训练工具函数模块

提供训练相关的实用工具函数
"""

import os
import json
import torch
import random
import numpy as np
from typing import Dict, Any, Optional, Tuple
import logging

from ..core import get_logger, TrainingError, ExceptionContext
from ..core.constants import ENV_VARS, DEFAULT_ENV_VALUES

logger = get_logger(__name__)


def setup_training_environment(
    seed: int = 42,
    cuda_visible_devices: Optional[str] = None,
    set_env_vars: bool = True
) -> Dict[str, Any]:
    """
    设置训练环境
    
    Args:
        seed: 随机种子
        cuda_visible_devices: CUDA可见设备
        set_env_vars: 是否设置环境变量
        
    Returns:
        Dict[str, Any]: 环境信息
    """
    with ExceptionContext("Setting up training environment"):
        # 设置随机种子
        set_seed(seed)
        
        # 设置环境变量
        if set_env_vars:
            setup_environment_variables(cuda_visible_devices)
        
        # 检查设备信息
        device_info = get_device_info()
        
        # 设置CUDA优化
        if torch.cuda.is_available():
            setup_cuda_optimizations()
        
        env_info = {
            "seed": seed,
            "device_info": device_info,
            "cuda_available": torch.cuda.is_available(),
            "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        }
        
        logger.info(f"Training environment setup completed: {env_info}")
        return env_info


def set_seed(seed: int):
    """
    设置随机种子
    
    Args:
        seed: 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        # 确保CUDA操作的确定性
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    logger.info(f"Random seed set to: {seed}")


def setup_environment_variables(cuda_visible_devices: Optional[str] = None):
    """
    设置环境变量
    
    Args:
        cuda_visible_devices: CUDA可见设备
    """
    # 设置CUDA设备
    if cuda_visible_devices:
        os.environ[ENV_VARS["CUDA_VISIBLE_DEVICES"]] = cuda_visible_devices
        logger.info(f"CUDA_VISIBLE_DEVICES set to: {cuda_visible_devices}")
    
    # 设置默认环境变量
    for var, value in DEFAULT_ENV_VALUES.items():
        if var not in os.environ:
            os.environ[var] = value
            logger.debug(f"Environment variable {var} set to: {value}")


def get_device_info() -> Dict[str, Any]:
    """
    获取设备信息
    
    Returns:
        Dict[str, Any]: 设备信息
    """
    device_info = {
        "cpu_count": os.cpu_count(),
        "cuda_available": torch.cuda.is_available(),
    }
    
    if torch.cuda.is_available():
        device_info.update({
            "cuda_device_count": torch.cuda.device_count(),
            "cuda_current_device": torch.cuda.current_device(),
            "cuda_devices": []
        })
        
        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            device_info["cuda_devices"].append({
                "device_id": i,
                "name": device_props.name,
                "total_memory": device_props.total_memory / 1024**3,  # GB
                "major": device_props.major,
                "minor": device_props.minor,
            })
    
    return device_info


def setup_cuda_optimizations():
    """设置CUDA优化"""
    try:
        # 启用TensorFloat-32 (TF32)
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        
        # 启用cuDNN基准测试
        torch.backends.cudnn.benchmark = True
        
        logger.info("CUDA optimizations enabled")
        
    except Exception as e:
        logger.warning(f"Failed to setup CUDA optimizations: {e}")


def calculate_training_steps(
    dataset_size: int,
    batch_size: int,
    num_epochs: int,
    gradient_accumulation_steps: int = 1,
    num_devices: int = 1
) -> Dict[str, int]:
    """
    计算训练步数
    
    Args:
        dataset_size: 数据集大小
        batch_size: 批次大小
        num_epochs: 训练轮数
        gradient_accumulation_steps: 梯度累积步数
        num_devices: 设备数量
        
    Returns:
        Dict[str, int]: 训练步数信息
    """
    effective_batch_size = batch_size * gradient_accumulation_steps * num_devices
    steps_per_epoch = max(1, dataset_size // effective_batch_size)
    total_steps = steps_per_epoch * num_epochs
    
    return {
        "dataset_size": dataset_size,
        "effective_batch_size": effective_batch_size,
        "steps_per_epoch": steps_per_epoch,
        "total_steps": total_steps,
        "num_epochs": num_epochs,
    }


def save_training_state(
    state_dict: Dict[str, Any],
    save_path: str,
    include_model: bool = False
):
    """
    保存训练状态
    
    Args:
        state_dict: 状态字典
        save_path: 保存路径
        include_model: 是否包含模型权重
    """
    with ExceptionContext(f"Saving training state to: {save_path}"):
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 准备保存的状态
        save_state = {
            "training_args": state_dict.get("training_args"),
            "model_config": state_dict.get("model_config"),
            "step": state_dict.get("step", 0),
            "epoch": state_dict.get("epoch", 0),
            "best_metric": state_dict.get("best_metric"),
            "optimizer_state": state_dict.get("optimizer_state"),
            "scheduler_state": state_dict.get("scheduler_state"),
        }
        
        if include_model:
            save_state["model_state"] = state_dict.get("model_state")
        
        # 保存为JSON（除了模型权重）
        json_path = save_path.replace(".pt", ".json").replace(".pth", ".json")
        json_state = {k: v for k, v in save_state.items() if k != "model_state"}
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_state, f, indent=2, default=str)
        
        # 保存完整状态（包括模型权重）
        if include_model:
            torch.save(save_state, save_path)
        
        logger.info(f"Training state saved to: {save_path}")


def load_training_state(load_path: str) -> Dict[str, Any]:
    """
    加载训练状态
    
    Args:
        load_path: 加载路径
        
    Returns:
        Dict[str, Any]: 训练状态
    """
    with ExceptionContext(f"Loading training state from: {load_path}"):
        if not os.path.exists(load_path):
            raise TrainingError(f"Training state file not found: {load_path}")
        
        if load_path.endswith(('.pt', '.pth')):
            # 加载PyTorch格式
            state = torch.load(load_path, map_location='cpu')
        else:
            # 加载JSON格式
            with open(load_path, 'r', encoding='utf-8') as f:
                state = json.load(f)
        
        logger.info(f"Training state loaded from: {load_path}")
        return state


def estimate_memory_usage(
    model_size: int,
    batch_size: int,
    sequence_length: int,
    precision: str = "fp16"
) -> Dict[str, float]:
    """
    估算内存使用量
    
    Args:
        model_size: 模型参数数量
        batch_size: 批次大小
        sequence_length: 序列长度
        precision: 精度类型
        
    Returns:
        Dict[str, float]: 内存使用估算（GB）
    """
    # 精度对应的字节数
    precision_bytes = {
        "fp32": 4,
        "fp16": 2,
        "bf16": 2,
        "int8": 1,
    }
    
    bytes_per_param = precision_bytes.get(precision, 4)
    
    # 模型权重内存
    model_memory = model_size * bytes_per_param / 1024**3
    
    # 优化器状态内存（Adam需要2倍模型参数）
    optimizer_memory = model_memory * 2
    
    # 梯度内存
    gradient_memory = model_memory
    
    # 激活值内存（粗略估算）
    activation_memory = (batch_size * sequence_length * 1024 * bytes_per_param) / 1024**3
    
    total_memory = model_memory + optimizer_memory + gradient_memory + activation_memory
    
    return {
        "model_memory": model_memory,
        "optimizer_memory": optimizer_memory,
        "gradient_memory": gradient_memory,
        "activation_memory": activation_memory,
        "total_memory": total_memory,
    }


def check_memory_requirements(
    model_size: int,
    batch_size: int,
    sequence_length: int,
    available_memory: float,
    precision: str = "fp16"
) -> Dict[str, Any]:
    """
    检查内存需求
    
    Args:
        model_size: 模型参数数量
        batch_size: 批次大小
        sequence_length: 序列长度
        available_memory: 可用内存（GB）
        precision: 精度类型
        
    Returns:
        Dict[str, Any]: 内存检查结果
    """
    memory_usage = estimate_memory_usage(model_size, batch_size, sequence_length, precision)
    
    required_memory = memory_usage["total_memory"]
    memory_sufficient = required_memory <= available_memory
    
    # 建议的批次大小
    if not memory_sufficient:
        suggested_batch_size = max(1, int(batch_size * available_memory / required_memory))
    else:
        suggested_batch_size = batch_size
    
    return {
        "memory_sufficient": memory_sufficient,
        "required_memory": required_memory,
        "available_memory": available_memory,
        "memory_usage_breakdown": memory_usage,
        "suggested_batch_size": suggested_batch_size,
        "memory_utilization": required_memory / available_memory if available_memory > 0 else float('inf'),
    }


def optimize_batch_size(
    model_size: int,
    available_memory: float,
    sequence_length: int = 2048,
    precision: str = "fp16",
    safety_margin: float = 0.9
) -> int:
    """
    优化批次大小
    
    Args:
        model_size: 模型参数数量
        available_memory: 可用内存（GB）
        sequence_length: 序列长度
        precision: 精度类型
        safety_margin: 安全边际
        
    Returns:
        int: 优化的批次大小
    """
    target_memory = available_memory * safety_margin
    
    # 二分搜索最优批次大小
    low, high = 1, 64
    best_batch_size = 1
    
    while low <= high:
        mid = (low + high) // 2
        memory_check = check_memory_requirements(
            model_size, mid, sequence_length, target_memory, precision
        )
        
        if memory_check["memory_sufficient"]:
            best_batch_size = mid
            low = mid + 1
        else:
            high = mid - 1
    
    logger.info(f"Optimized batch size: {best_batch_size}")
    return best_batch_size
