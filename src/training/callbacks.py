"""
训练回调函数模块

定义各种训练回调函数
"""

import os
import time
from typing import Dict, Any, List, Optional
import logging

from ..core import get_logger, TrainingError, ExceptionContext

logger = get_logger(__name__)

# 尝试导入可选依赖
try:
    import swanlab
    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False

try:
    from transformers import TrainerCallback, TrainerState, TrainerControl
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    # 定义占位符类
    class TrainerCallback:
        pass
    class TrainerState:
        pass
    class TrainerControl:
        pass


class NavigationCallback(TrainerCallback):
    """导航训练基础回调"""
    
    def __init__(self, log_interval: int = 10):
        """
        初始化回调
        
        Args:
            log_interval: 日志记录间隔
        """
        self.log_interval = log_interval
        self.start_time = None
        self.step_times = []
    
    def on_train_begin(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """训练开始时调用"""
        self.start_time = time.time()
        logger.info("Training started")
        logger.info(f"Total training steps: {state.max_steps}")
        logger.info(f"Training epochs: {args.num_train_epochs}")
    
    def on_train_end(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """训练结束时调用"""
        if self.start_time:
            total_time = time.time() - self.start_time
            logger.info(f"Training completed in {total_time:.2f} seconds")
            
            if self.step_times:
                avg_step_time = sum(self.step_times) / len(self.step_times)
                logger.info(f"Average step time: {avg_step_time:.3f} seconds")
    
    def on_step_begin(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """步骤开始时调用"""
        self.step_start_time = time.time()
    
    def on_step_end(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """步骤结束时调用"""
        if hasattr(self, 'step_start_time'):
            step_time = time.time() - self.step_start_time
            self.step_times.append(step_time)
            
            # 保持最近100步的时间记录
            if len(self.step_times) > 100:
                self.step_times = self.step_times[-100:]
    
    def on_log(self, args, state: TrainerState, control: TrainerControl, logs=None, **kwargs):
        """日志记录时调用"""
        if logs and state.global_step % self.log_interval == 0:
            # 添加自定义指标
            if self.step_times:
                logs["avg_step_time"] = sum(self.step_times[-10:]) / min(10, len(self.step_times))
            
            # 计算剩余时间
            if self.start_time and state.max_steps > 0:
                elapsed_time = time.time() - self.start_time
                progress = state.global_step / state.max_steps
                if progress > 0:
                    estimated_total_time = elapsed_time / progress
                    remaining_time = estimated_total_time - elapsed_time
                    logs["estimated_remaining_time"] = remaining_time
    
    def on_save(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """保存检查点时调用"""
        logger.info(f"Checkpoint saved at step {state.global_step}")


class SwanLabCallback(TrainerCallback):
    """SwanLab实验跟踪回调"""
    
    def __init__(
        self,
        project_name: str = "navigation-training",
        experiment_name: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化SwanLab回调
        
        Args:
            project_name: 项目名称
            experiment_name: 实验名称
            config: 配置信息
        """
        self.project_name = project_name
        self.experiment_name = experiment_name
        self.config = config or {}
        self.run = None
        
        if not SWANLAB_AVAILABLE:
            logger.warning("SwanLab not available, callback will be disabled")
    
    def on_train_begin(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """训练开始时初始化SwanLab"""
        if not SWANLAB_AVAILABLE:
            return
        
        try:
            # 合并配置
            full_config = {
                **self.config,
                "learning_rate": args.learning_rate,
                "batch_size": args.per_device_train_batch_size,
                "num_epochs": args.num_train_epochs,
                "gradient_accumulation_steps": args.gradient_accumulation_steps,
            }
            
            self.run = swanlab.init(
                project=self.project_name,
                experiment_name=self.experiment_name,
                config=full_config
            )
            
            logger.info("SwanLab experiment initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize SwanLab: {e}")
    
    def on_log(self, args, state: TrainerState, control: TrainerControl, logs=None, **kwargs):
        """记录日志到SwanLab"""
        if not SWANLAB_AVAILABLE or not self.run or not logs:
            return
        
        try:
            # 过滤和转换日志
            swanlab_logs = {}
            for key, value in logs.items():
                if isinstance(value, (int, float)):
                    swanlab_logs[key] = value
            
            if swanlab_logs:
                swanlab.log(swanlab_logs, step=state.global_step)
                
        except Exception as e:
            logger.error(f"Failed to log to SwanLab: {e}")
    
    def on_train_end(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """训练结束时关闭SwanLab"""
        if self.run:
            try:
                swanlab.finish()
                logger.info("SwanLab experiment finished")
            except Exception as e:
                logger.error(f"Failed to finish SwanLab experiment: {e}")


class WandBCallback(TrainerCallback):
    """Weights & Biases实验跟踪回调"""
    
    def __init__(
        self,
        project_name: str = "navigation-training",
        run_name: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化WandB回调
        
        Args:
            project_name: 项目名称
            run_name: 运行名称
            config: 配置信息
        """
        self.project_name = project_name
        self.run_name = run_name
        self.config = config or {}
        
        if not WANDB_AVAILABLE:
            logger.warning("WandB not available, callback will be disabled")
    
    def on_train_begin(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """训练开始时初始化WandB"""
        if not WANDB_AVAILABLE:
            return
        
        try:
            # 合并配置
            full_config = {
                **self.config,
                "learning_rate": args.learning_rate,
                "batch_size": args.per_device_train_batch_size,
                "num_epochs": args.num_train_epochs,
                "gradient_accumulation_steps": args.gradient_accumulation_steps,
            }
            
            wandb.init(
                project=self.project_name,
                name=self.run_name,
                config=full_config
            )
            
            logger.info("WandB run initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize WandB: {e}")
    
    def on_log(self, args, state: TrainerState, control: TrainerControl, logs=None, **kwargs):
        """记录日志到WandB"""
        if not WANDB_AVAILABLE or not logs:
            return
        
        try:
            # 过滤和转换日志
            wandb_logs = {}
            for key, value in logs.items():
                if isinstance(value, (int, float)):
                    wandb_logs[key] = value
            
            if wandb_logs:
                wandb.log(wandb_logs, step=state.global_step)
                
        except Exception as e:
            logger.error(f"Failed to log to WandB: {e}")
    
    def on_train_end(self, args, state: TrainerState, control: TrainerControl, **kwargs):
        """训练结束时关闭WandB"""
        try:
            wandb.finish()
            logger.info("WandB run finished")
        except Exception as e:
            logger.error(f"Failed to finish WandB run: {e}")


def setup_callbacks(
    use_swanlab: bool = False,
    use_wandb: bool = False,
    swanlab_config: Optional[Dict[str, Any]] = None,
    wandb_config: Optional[Dict[str, Any]] = None,
    custom_callbacks: Optional[List[TrainerCallback]] = None
) -> List[TrainerCallback]:
    """
    设置训练回调函数
    
    Args:
        use_swanlab: 是否使用SwanLab
        use_wandb: 是否使用WandB
        swanlab_config: SwanLab配置
        wandb_config: WandB配置
        custom_callbacks: 自定义回调列表
        
    Returns:
        List[TrainerCallback]: 回调函数列表
    """
    callbacks = []
    
    # 添加基础回调
    callbacks.append(NavigationCallback())
    
    # 添加SwanLab回调
    if use_swanlab and SWANLAB_AVAILABLE:
        swanlab_callback = SwanLabCallback(**(swanlab_config or {}))
        callbacks.append(swanlab_callback)
        logger.info("SwanLab callback added")
    elif use_swanlab:
        logger.warning("SwanLab requested but not available")
    
    # 添加WandB回调
    if use_wandb and WANDB_AVAILABLE:
        wandb_callback = WandBCallback(**(wandb_config or {}))
        callbacks.append(wandb_callback)
        logger.info("WandB callback added")
    elif use_wandb:
        logger.warning("WandB requested but not available")
    
    # 添加自定义回调
    if custom_callbacks:
        callbacks.extend(custom_callbacks)
        logger.info(f"Added {len(custom_callbacks)} custom callbacks")
    
    logger.info(f"Total callbacks configured: {len(callbacks)}")
    return callbacks


def setup_swanlab(
    project_name: str = "navigation-training",
    experiment_name: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None
) -> Optional[SwanLabCallback]:
    """
    设置SwanLab的便捷函数
    
    Args:
        project_name: 项目名称
        experiment_name: 实验名称
        config: 配置信息
        
    Returns:
        Optional[SwanLabCallback]: SwanLab回调或None
    """
    if SWANLAB_AVAILABLE:
        return SwanLabCallback(
            project_name=project_name,
            experiment_name=experiment_name,
            config=config
        )
    else:
        logger.warning("SwanLab not available")
        return None


def setup_wandb(
    project_name: str = "navigation-training",
    run_name: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None
) -> Optional[WandBCallback]:
    """
    设置WandB的便捷函数
    
    Args:
        project_name: 项目名称
        run_name: 运行名称
        config: 配置信息
        
    Returns:
        Optional[WandBCallback]: WandB回调或None
    """
    if WANDB_AVAILABLE:
        return WandBCallback(
            project_name=project_name,
            run_name=run_name,
            config=config
        )
    else:
        logger.warning("WandB not available")
        return None
