"""
训练参数配置模块

定义训练相关的参数和配置
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
import logging

from ..core import get_logger, ConfigurationError
from ..core.constants import (
    DEFAULT_BATCH_SIZE,
    DEFAULT_LEARNING_RATE,
    DEFAULT_NUM_EPOCHS,
    DEFAULT_WARMUP_RATIO,
    DEFAULT_WEIGHT_DECAY,
    DEFAULT_OUTPUT_DIR,
    OptimizerType,
    SchedulerType,
    PrecisionType
)

logger = get_logger(__name__)


@dataclass
class TrainingConfig:
    """训练配置类"""
    
    # 基础训练参数
    output_dir: str = DEFAULT_OUTPUT_DIR
    num_train_epochs: int = DEFAULT_NUM_EPOCHS
    per_device_train_batch_size: int = DEFAULT_BATCH_SIZE
    per_device_eval_batch_size: int = DEFAULT_BATCH_SIZE
    gradient_accumulation_steps: int = 4
    
    # 学习率和优化器
    learning_rate: float = DEFAULT_LEARNING_RATE
    weight_decay: float = DEFAULT_WEIGHT_DECAY
    warmup_ratio: float = DEFAULT_WARMUP_RATIO
    lr_scheduler_type: str = "cosine"
    optimizer_type: str = "adamw"
    
    # 精度和性能
    fp16: bool = False
    bf16: bool = True
    gradient_checkpointing: bool = True
    dataloader_pin_memory: bool = False
    dataloader_num_workers: int = 0
    
    # 保存和日志
    save_steps: int = 500
    save_total_limit: int = 3
    logging_steps: int = 10
    evaluation_strategy: str = "no"
    eval_steps: Optional[int] = None
    
    # 分布式训练
    ddp_find_unused_parameters: bool = False
    ddp_timeout: int = 1800
    
    # 其他设置
    remove_unused_columns: bool = False
    report_to: str = "none"
    seed: int = 42
    resume_from_checkpoint: Optional[str] = None
    
    # 早停和监控
    load_best_model_at_end: bool = False
    metric_for_best_model: str = "loss"
    greater_is_better: bool = False
    
    # 自定义参数
    max_length: int = 2048
    ignore_data_skip: bool = False
    
    def __post_init__(self):
        """后处理初始化"""
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 验证配置
        self.validate()
    
    def validate(self):
        """验证配置有效性"""
        errors = []
        
        # 验证基础参数
        if self.num_train_epochs <= 0:
            errors.append("num_train_epochs must be positive")
        
        if self.per_device_train_batch_size <= 0:
            errors.append("per_device_train_batch_size must be positive")
        
        if self.learning_rate <= 0:
            errors.append("learning_rate must be positive")
        
        if not (0 <= self.warmup_ratio <= 1):
            errors.append("warmup_ratio must be between 0 and 1")
        
        # 验证调度器类型
        valid_schedulers = [e.value for e in SchedulerType]
        if self.lr_scheduler_type not in valid_schedulers:
            errors.append(f"lr_scheduler_type must be one of: {valid_schedulers}")
        
        # 验证优化器类型
        valid_optimizers = [e.value for e in OptimizerType]
        if self.optimizer_type not in valid_optimizers:
            errors.append(f"optimizer_type must be one of: {valid_optimizers}")
        
        # 验证精度设置
        if self.fp16 and self.bf16:
            errors.append("Cannot use both fp16 and bf16")
        
        # 验证评估策略
        valid_eval_strategies = ["no", "steps", "epoch"]
        if self.evaluation_strategy not in valid_eval_strategies:
            errors.append(f"evaluation_strategy must be one of: {valid_eval_strategies}")
        
        if self.evaluation_strategy == "steps" and self.eval_steps is None:
            errors.append("eval_steps must be specified when evaluation_strategy is 'steps'")
        
        if errors:
            for error in errors:
                logger.error(f"Training config validation error: {error}")
            raise ConfigurationError("Training configuration validation failed")
    
    def get_effective_batch_size(self, num_devices: int = 1) -> int:
        """
        计算有效批次大小
        
        Args:
            num_devices: 设备数量
            
        Returns:
            int: 有效批次大小
        """
        return (
            self.per_device_train_batch_size * 
            self.gradient_accumulation_steps * 
            num_devices
        )
    
    def to_training_arguments(self):
        """转换为TrainingArguments"""
        try:
            from transformers import TrainingArguments
            
            return TrainingArguments(
                output_dir=self.output_dir,
                num_train_epochs=self.num_train_epochs,
                per_device_train_batch_size=self.per_device_train_batch_size,
                per_device_eval_batch_size=self.per_device_eval_batch_size,
                gradient_accumulation_steps=self.gradient_accumulation_steps,
                learning_rate=self.learning_rate,
                weight_decay=self.weight_decay,
                warmup_ratio=self.warmup_ratio,
                lr_scheduler_type=self.lr_scheduler_type,
                fp16=self.fp16,
                bf16=self.bf16,
                gradient_checkpointing=self.gradient_checkpointing,
                dataloader_pin_memory=self.dataloader_pin_memory,
                dataloader_num_workers=self.dataloader_num_workers,
                save_steps=self.save_steps,
                save_total_limit=self.save_total_limit,
                logging_steps=self.logging_steps,
                evaluation_strategy=self.evaluation_strategy,
                eval_steps=self.eval_steps,
                ddp_find_unused_parameters=self.ddp_find_unused_parameters,
                remove_unused_columns=self.remove_unused_columns,
                report_to=self.report_to,
                seed=self.seed,
                load_best_model_at_end=self.load_best_model_at_end,
                metric_for_best_model=self.metric_for_best_model,
                greater_is_better=self.greater_is_better,
                ignore_data_skip=self.ignore_data_skip,
            )
        except ImportError:
            raise ConfigurationError("transformers library not available")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "output_dir": self.output_dir,
            "num_train_epochs": self.num_train_epochs,
            "per_device_train_batch_size": self.per_device_train_batch_size,
            "per_device_eval_batch_size": self.per_device_eval_batch_size,
            "gradient_accumulation_steps": self.gradient_accumulation_steps,
            "learning_rate": self.learning_rate,
            "weight_decay": self.weight_decay,
            "warmup_ratio": self.warmup_ratio,
            "lr_scheduler_type": self.lr_scheduler_type,
            "optimizer_type": self.optimizer_type,
            "fp16": self.fp16,
            "bf16": self.bf16,
            "gradient_checkpointing": self.gradient_checkpointing,
            "save_steps": self.save_steps,
            "logging_steps": self.logging_steps,
            "seed": self.seed,
            "max_length": self.max_length,
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "TrainingConfig":
        """从字典创建配置"""
        return cls(**config_dict)


def create_training_arguments(
    output_dir: str = DEFAULT_OUTPUT_DIR,
    num_train_epochs: int = DEFAULT_NUM_EPOCHS,
    per_device_train_batch_size: int = DEFAULT_BATCH_SIZE,
    learning_rate: float = DEFAULT_LEARNING_RATE,
    **kwargs
):
    """
    创建训练参数的便捷函数
    
    Args:
        output_dir: 输出目录
        num_train_epochs: 训练轮数
        per_device_train_batch_size: 每设备批次大小
        learning_rate: 学习率
        **kwargs: 其他参数
        
    Returns:
        TrainingArguments: 训练参数
    """
    config = TrainingConfig(
        output_dir=output_dir,
        num_train_epochs=num_train_epochs,
        per_device_train_batch_size=per_device_train_batch_size,
        learning_rate=learning_rate,
        **kwargs
    )
    
    return config.to_training_arguments()


def create_multi_gpu_training_arguments(
    output_dir: str = "results_multi_gpu",
    num_train_epochs: int = 1,
    per_device_train_batch_size: int = 4,
    gradient_accumulation_steps: int = 2,
    learning_rate: float = 2e-4,
    **kwargs
):
    """
    创建多GPU训练参数
    
    Args:
        output_dir: 输出目录
        num_train_epochs: 训练轮数
        per_device_train_batch_size: 每设备批次大小
        gradient_accumulation_steps: 梯度累积步数
        learning_rate: 学习率
        **kwargs: 其他参数
        
    Returns:
        TrainingArguments: 多GPU训练参数
    """
    # 多GPU训练的特殊设置
    multi_gpu_kwargs = {
        "ddp_find_unused_parameters": False,
        "dataloader_num_workers": 0,  # 避免多进程问题
        "dataloader_pin_memory": False,
        "bf16": True,  # 使用bfloat16
        "gradient_checkpointing": True,
        **kwargs
    }
    
    return create_training_arguments(
        output_dir=output_dir,
        num_train_epochs=num_train_epochs,
        per_device_train_batch_size=per_device_train_batch_size,
        gradient_accumulation_steps=gradient_accumulation_steps,
        learning_rate=learning_rate,
        **multi_gpu_kwargs
    )
