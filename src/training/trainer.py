"""
训练器模块

定义导航任务专用的训练器
"""

import torch
from typing import Dict, Any, Optional, List, Union
import logging

from ..core import get_logger, TrainingError, ExceptionContext
from ..data import NavigationDataset, create_collate_fn
from ..models import NavigationTokenizer
from .callbacks import setup_callbacks

logger = get_logger(__name__)

try:
    from transformers import Trainer, TrainingArguments
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    # 定义占位符类
    class Trainer:
        pass


class NavigationTrainer(Trainer):
    """导航任务专用训练器"""
    
    def __init__(
        self,
        model,
        args: TrainingArguments,
        train_dataset: NavigationDataset,
        eval_dataset: Optional[NavigationDataset] = None,
        tokenizer=None,
        nav_tokenizer: Optional[NavigationTokenizer] = None,
        data_collator=None,
        callbacks: Optional[List] = None,
        **kwargs
    ):
        """
        初始化导航训练器
        
        Args:
            model: 模型
            args: 训练参数
            train_dataset: 训练数据集
            eval_dataset: 验证数据集
            tokenizer: 分词器
            nav_tokenizer: 导航分词器
            data_collator: 数据整理器
            callbacks: 回调函数列表
            **kwargs: 其他参数
        """
        if not TRANSFORMERS_AVAILABLE:
            raise TrainingError("transformers library not available")
        
        self.nav_tokenizer = nav_tokenizer
        self.navigation_token_ids = {}
        
        if nav_tokenizer:
            self.navigation_token_ids = nav_tokenizer.get_navigation_token_ids()
        
        # 设置数据整理器
        if data_collator is None:
            data_collator = create_collate_fn(tokenizer=tokenizer)
        
        # 设置回调函数
        if callbacks is None:
            callbacks = setup_callbacks()
        
        # 初始化基类
        super().__init__(
            model=model,
            args=args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=tokenizer,
            data_collator=data_collator,
            callbacks=callbacks,
            **kwargs
        )
        
        logger.info("NavigationTrainer initialized")
    
    def compute_loss(self, model, inputs, return_outputs=False):
        """
        计算损失函数
        
        Args:
            model: 模型
            inputs: 输入数据
            return_outputs: 是否返回输出
            
        Returns:
            损失值或(损失值, 输出)
        """
        try:
            # 前向传播
            outputs = model(**inputs)
            
            # 获取损失
            if "labels" in inputs:
                loss = outputs.loss
            else:
                # 如果没有labels，使用logits计算损失
                logits = outputs.logits
                labels = inputs.get("input_ids")
                if labels is not None:
                    # 移位标签用于语言建模
                    shift_logits = logits[..., :-1, :].contiguous()
                    shift_labels = labels[..., 1:].contiguous()
                    
                    # 计算交叉熵损失
                    loss_fct = torch.nn.CrossEntropyLoss(ignore_index=-100)
                    loss = loss_fct(
                        shift_logits.view(-1, shift_logits.size(-1)),
                        shift_labels.view(-1)
                    )
                else:
                    loss = outputs.loss if hasattr(outputs, 'loss') else torch.tensor(0.0)
            
            return (loss, outputs) if return_outputs else loss
            
        except Exception as e:
            logger.error(f"Error computing loss: {e}")
            raise TrainingError(f"Loss computation failed: {e}", cause=e)
    
    def evaluate(
        self,
        eval_dataset: Optional[NavigationDataset] = None,
        ignore_keys: Optional[List[str]] = None,
        metric_key_prefix: str = "eval"
    ) -> Dict[str, float]:
        """
        评估模型
        
        Args:
            eval_dataset: 评估数据集
            ignore_keys: 忽略的键
            metric_key_prefix: 指标前缀
            
        Returns:
            Dict[str, float]: 评估指标
        """
        with ExceptionContext("Model evaluation"):
            # 使用基类的评估方法
            eval_results = super().evaluate(
                eval_dataset=eval_dataset,
                ignore_keys=ignore_keys,
                metric_key_prefix=metric_key_prefix
            )
            
            # 添加自定义指标
            if self.nav_tokenizer and eval_dataset:
                custom_metrics = self._compute_navigation_metrics(eval_dataset)
                eval_results.update({
                    f"{metric_key_prefix}_{k}": v for k, v in custom_metrics.items()
                })
            
            return eval_results
    
    def _compute_navigation_metrics(self, eval_dataset: NavigationDataset) -> Dict[str, float]:
        """
        计算导航任务专用指标
        
        Args:
            eval_dataset: 评估数据集
            
        Returns:
            Dict[str, float]: 导航指标
        """
        metrics = {}
        
        try:
            # 统计动作分布
            action_counts = {}
            for sample in eval_dataset.samples:
                action = sample.get("action", "unknown")
                action_counts[action] = action_counts.get(action, 0) + 1
            
            total_samples = len(eval_dataset.samples)
            for action, count in action_counts.items():
                metrics[f"action_ratio_{action}"] = count / total_samples if total_samples > 0 else 0
            
            # 添加数据集统计
            metrics["dataset_size"] = total_samples
            metrics["unique_actions"] = len(action_counts)
            
        except Exception as e:
            logger.warning(f"Failed to compute navigation metrics: {e}")
        
        return metrics
    
    def save_model(self, output_dir: Optional[str] = None, _internal_call: bool = False):
        """
        保存模型
        
        Args:
            output_dir: 输出目录
            _internal_call: 内部调用标志
        """
        with ExceptionContext("Saving model"):
            # 调用基类保存方法
            super().save_model(output_dir=output_dir, _internal_call=_internal_call)
            
            # 保存导航分词器
            if self.nav_tokenizer and output_dir:
                try:
                    nav_tokenizer_path = f"{output_dir}/navigation_tokenizer"
                    self.nav_tokenizer.get_tokenizer().save_pretrained(nav_tokenizer_path)
                    logger.info(f"Navigation tokenizer saved to: {nav_tokenizer_path}")
                except Exception as e:
                    logger.warning(f"Failed to save navigation tokenizer: {e}")
    
    def get_train_dataloader(self):
        """获取训练数据加载器"""
        try:
            return super().get_train_dataloader()
        except Exception as e:
            logger.error(f"Error creating train dataloader: {e}")
            raise TrainingError(f"Failed to create train dataloader: {e}", cause=e)
    
    def get_eval_dataloader(self, eval_dataset=None):
        """获取评估数据加载器"""
        try:
            return super().get_eval_dataloader(eval_dataset)
        except Exception as e:
            logger.error(f"Error creating eval dataloader: {e}")
            raise TrainingError(f"Failed to create eval dataloader: {e}", cause=e)
    
    def log(self, logs: Dict[str, float]) -> None:
        """
        记录日志
        
        Args:
            logs: 日志字典
        """
        # 添加自定义日志信息
        if self.state.global_step > 0:
            # 添加学习率信息
            if hasattr(self.lr_scheduler, 'get_last_lr'):
                logs["learning_rate"] = self.lr_scheduler.get_last_lr()[0]
            
            # 添加GPU内存使用信息
            if torch.cuda.is_available():
                logs["gpu_memory_allocated"] = torch.cuda.memory_allocated() / 1024**3  # GB
                logs["gpu_memory_cached"] = torch.cuda.memory_reserved() / 1024**3  # GB
        
        # 调用基类日志方法
        super().log(logs)
    
    def training_step(self, model, inputs):
        """
        训练步骤
        
        Args:
            model: 模型
            inputs: 输入数据
            
        Returns:
            损失值
        """
        try:
            return super().training_step(model, inputs)
        except Exception as e:
            logger.error(f"Error in training step: {e}")
            raise TrainingError(
                f"Training step failed at step {self.state.global_step}: {e}",
                step=self.state.global_step,
                cause=e
            )


def create_trainer(
    model,
    train_dataset: NavigationDataset,
    eval_dataset: Optional[NavigationDataset] = None,
    training_args: Optional[TrainingArguments] = None,
    tokenizer=None,
    nav_tokenizer: Optional[NavigationTokenizer] = None,
    callbacks: Optional[List] = None,
    **kwargs
) -> NavigationTrainer:
    """
    创建导航训练器的便捷函数
    
    Args:
        model: 模型
        train_dataset: 训练数据集
        eval_dataset: 验证数据集
        training_args: 训练参数
        tokenizer: 分词器
        nav_tokenizer: 导航分词器
        callbacks: 回调函数
        **kwargs: 其他参数
        
    Returns:
        NavigationTrainer: 导航训练器
    """
    if training_args is None:
        from .arguments import create_training_arguments
        training_args = create_training_arguments()
    
    return NavigationTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        nav_tokenizer=nav_tokenizer,
        callbacks=callbacks,
        **kwargs
    )
