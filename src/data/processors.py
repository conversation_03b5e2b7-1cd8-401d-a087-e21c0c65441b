"""
数据预处理器模块

负责数据的预处理和转换
"""

import os
from typing import Dict, Any, List, Optional, Tuple, Union
from PIL import Image
import torch
import logging

from ..core import get_logger, DataProcessingError, ExceptionContext
from ..core.constants import (
    ACTION_TOKEN_MAP,
    DEFAULT_IMAGE_SIZE,
    VISION_START_TOKEN,
    VISION_END_TOKEN,
    IMAGE_PAD_TOKEN,
)

logger = get_logger(__name__)

# 尝试导入官方工具
try:
    from qwen_vl_utils import process_vision_info

    QWEN_VL_UTILS_AVAILABLE = True
    logger.info("qwen_vl_utils imported successfully")
except ImportError:
    QWEN_VL_UTILS_AVAILABLE = False
    logger.warning("qwen_vl_utils not available")


class DataProcessor:
    """基础数据处理器"""

    def __init__(self, processor=None):
        """
        初始化数据处理器

        Args:
            processor: 外部处理器（如Qwen2VLProcessor）
        """
        self.processor = processor

    def process_sample(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个样本

        Args:
            sample: 输入样本

        Returns:
            Dict: 处理后的样本
        """
        raise NotImplementedError("Subclasses must implement process_sample")

    def batch_process(self, samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量处理样本

        Args:
            samples: 样本列表

        Returns:
            List[Dict]: 处理后的样本列表
        """
        processed_samples = []

        for i, sample in enumerate(samples):
            try:
                processed_sample = self.process_sample(sample)
                processed_samples.append(processed_sample)
            except Exception as e:
                logger.error(f"Failed to process sample {i}: {e}")
                continue

        return processed_samples


class ImageProcessor(DataProcessor):
    """图像处理器"""

    def __init__(self, image_size: Tuple[int, int] = DEFAULT_IMAGE_SIZE, **kwargs):
        """
        初始化图像处理器

        Args:
            image_size: 目标图像尺寸
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.image_size = image_size

    def load_image(self, image_path: str) -> Image.Image:
        """
        加载图像

        Args:
            image_path: 图像路径

        Returns:
            Image.Image: PIL图像对象
        """
        with ExceptionContext(f"Loading image: {image_path}"):
            if not os.path.exists(image_path):
                raise DataProcessingError(
                    f"Image file does not exist: {image_path}", data_path=image_path
                )

            image = Image.open(image_path).convert("RGB")
            return image

    def resize_image(self, image: Image.Image) -> Image.Image:
        """
        调整图像尺寸

        Args:
            image: 输入图像

        Returns:
            Image.Image: 调整后的图像
        """
        return image.resize(self.image_size, Image.Resampling.LANCZOS)

    def process_image(self, image_path: str) -> Image.Image:
        """
        处理图像

        Args:
            image_path: 图像路径

        Returns:
            Image.Image: 处理后的图像
        """
        image = self.load_image(image_path)
        image = self.resize_image(image)
        return image

    def create_placeholder_image(self) -> Image.Image:
        """
        创建占位符图像

        Returns:
            Image.Image: 占位符图像
        """
        return Image.new("RGB", self.image_size, color=(0, 0, 0))


class TextProcessor(DataProcessor):
    """文本处理器"""

    def __init__(self, max_length: int = 2048, **kwargs):
        """
        初始化文本处理器

        Args:
            max_length: 最大文本长度
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        self.max_length = max_length

    def process_navigation_action(self, action: str) -> str:
        """
        处理导航动作

        Args:
            action: 动作字符串

        Returns:
            str: 处理后的动作token
        """
        if action in ACTION_TOKEN_MAP:
            return ACTION_TOKEN_MAP[action]
        else:
            logger.warning(f"Unknown action: {action}")
            return action

    def create_messages_format(
        self, image_path: str, question: str, target: str
    ) -> List[Dict[str, Any]]:
        """
        创建标准的messages格式

        Args:
            image_path: 图像路径
            question: 问题文本
            target: 目标回答

        Returns:
            List[Dict]: messages格式数据
        """
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": image_path},
                    {"type": "text", "text": question},
                ],
            },
            {"role": "assistant", "content": target},
        ]

        return messages

    def process_vision_content(self, content: str, grid_thw: int = 1) -> str:
        """
        处理视觉内容，添加视觉token

        Args:
            content: 原始内容
            grid_thw: 网格大小

        Returns:
            str: 处理后的内容
        """
        if "<image>" in content:
            # 替换<image>为视觉token
            replacement = (
                VISION_START_TOKEN + IMAGE_PAD_TOKEN * grid_thw + VISION_END_TOKEN
            )
            content = content.replace("<image>", replacement)

        return content


class NavigationProcessor(DataProcessor):
    """导航数据处理器"""

    def __init__(
        self,
        processor=None,
        image_size: Tuple[int, int] = DEFAULT_IMAGE_SIZE,
        max_length: int = 2048,
        **kwargs,
    ):
        """
        初始化导航数据处理器

        Args:
            processor: 外部处理器
            image_size: 图像尺寸
            max_length: 最大长度
            **kwargs: 其他参数
        """
        super().__init__(processor, **kwargs)
        self.image_processor = ImageProcessor(image_size)
        self.text_processor = TextProcessor(max_length)

    def process_sample(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理导航样本

        Args:
            sample: 输入样本

        Returns:
            Dict: 处理后的样本
        """
        with ExceptionContext("Processing navigation sample"):
            # 提取基本信息
            image_path = sample["image_path"]
            question = sample["question"]
            target = sample["target"]

            # 创建messages格式
            messages = self.text_processor.create_messages_format(
                image_path, question, target
            )

            # 如果有官方处理器，使用官方处理
            if self.processor and QWEN_VL_UTILS_AVAILABLE:
                return self._process_with_official_tools(sample, messages)
            else:
                return self._process_simple(sample, messages)

    def _process_with_official_tools(
        self, sample: Dict[str, Any], messages: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        使用官方工具处理

        Args:
            sample: 原始样本
            messages: messages格式数据

        Returns:
            Dict: 处理后的样本
        """
        try:
            # 使用官方工具处理
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=False
            )

            image_inputs, video_inputs = process_vision_info(messages)

            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )

            result = {
                "input_ids": inputs["input_ids"].squeeze(0),
                "attention_mask": inputs["attention_mask"].squeeze(0),
                "pixel_values": inputs["pixel_values"].squeeze(0),
                "messages": messages,
                **sample,  # 保留原始信息
            }

            # 添加image_grid_thw如果存在
            if "image_grid_thw" in inputs:
                result["image_grid_thw"] = inputs["image_grid_thw"].squeeze(0)

            return result

        except Exception as e:
            logger.error(f"Error processing with official tools: {e}")
            # 回退到简单处理
            return self._process_simple(sample, messages)

    def _process_simple(
        self, sample: Dict[str, Any], messages: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        简单处理模式

        Args:
            sample: 原始样本
            messages: messages格式数据

        Returns:
            Dict: 处理后的样本
        """
        try:
            # 处理图像
            image = self.image_processor.process_image(sample["image_path"])
        except Exception as e:
            logger.error(f"Error loading image {sample['image_path']}: {e}")
            image = self.image_processor.create_placeholder_image()

        return {"messages": messages, "image": image, **sample}  # 保留原始信息

    def create_training_sample(
        self,
        scene_dir: str,
        question: str,
        answer: str,
        path: List[Dict[str, Any]],
        step_index: int,
    ) -> Optional[Dict[str, Any]]:
        """
        创建训练样本

        Args:
            scene_dir: 场景目录
            question: 问题
            answer: 答案
            path: 路径数据
            step_index: 步骤索引

        Returns:
            Optional[Dict]: 训练样本或None
        """
        if step_index >= len(path):
            return None

        step = path[step_index]
        img_path = os.path.join(scene_dir, step.get("img", ""))
        action = step.get("action", "")

        if not os.path.exists(img_path):
            return None

        # 构建目标输出
        if action in ["move_forward", "turn_left", "turn_right"]:
            target = self.text_processor.process_navigation_action(action)
        elif action == "stop":
            target = answer
        else:
            return None

        sample = {
            "image_path": img_path,
            "target": target,
            "action": action,
            "question": question,
            "answer": answer,
            "step_index": step_index,
            "total_steps": len(path),
        }

        return self.process_sample(sample)

    def extract_samples_from_scene_data(
        self, scene_data: List[Dict[str, Any]], scene_dir: str
    ) -> List[Dict[str, Any]]:
        """
        从场景数据中提取训练样本

        Args:
            scene_data: 场景数据
            scene_dir: 场景目录

        Returns:
            List[Dict]: 训练样本列表
        """
        samples = []

        for data_item in scene_data:
            question = data_item.get("question", "")
            answer = data_item.get("answer", "")
            path = data_item.get("path", [])

            if not question or not answer or not path:
                continue

            # 为每个路径步骤创建训练样本
            for i in range(len(path)):
                sample = self.create_training_sample(
                    scene_dir, question, answer, path, i
                )
                if sample:
                    samples.append(sample)

        return samples
