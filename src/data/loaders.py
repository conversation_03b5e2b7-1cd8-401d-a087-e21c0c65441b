"""
数据加载器模块

负责从文件系统加载和验证数据
"""

import os
import json
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import logging

from ..core import get_logger, DataProcessingError, ExceptionContext
from ..core.constants import SUPPORTED_IMAGE_FORMATS, DATASET_SPLITS

logger = get_logger(__name__)


class DataLoader:
    """基础数据加载器"""
    
    def __init__(self, data_root: str, validate_paths: bool = True):
        """
        初始化数据加载器
        
        Args:
            data_root: 数据根目录
            validate_paths: 是否验证路径有效性
        """
        self.data_root = Path(data_root)
        self.validate_paths = validate_paths
        
        if validate_paths and not self.data_root.exists():
            raise DataProcessingError(
                f"Data root directory does not exist: {data_root}",
                data_path=data_root
            )
    
    def load_json(self, file_path: str) -> Dict[str, Any]:
        """
        加载JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            Dict: JSON数据
        """
        with ExceptionContext(f"Loading JSON file: {file_path}"):
            if not os.path.isabs(file_path):
                file_path = self.data_root / file_path
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.debug(f"Loaded JSON file: {file_path}")
            return data
    
    def validate_image_path(self, image_path: str) -> bool:
        """
        验证图像路径有效性
        
        Args:
            image_path: 图像路径
            
        Returns:
            bool: 是否有效
        """
        if not os.path.isabs(image_path):
            image_path = self.data_root / image_path
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return False
        
        # 检查文件扩展名
        ext = os.path.splitext(image_path)[1].lower()
        return ext in SUPPORTED_IMAGE_FORMATS
    
    def list_files(self, directory: str, extensions: List[str] = None) -> List[str]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            extensions: 文件扩展名列表
            
        Returns:
            List[str]: 文件路径列表
        """
        if not os.path.isabs(directory):
            directory = self.data_root / directory
        
        if not os.path.exists(directory):
            logger.warning(f"Directory does not exist: {directory}")
            return []
        
        files = []
        for file_path in Path(directory).rglob("*"):
            if file_path.is_file():
                if extensions is None or file_path.suffix.lower() in extensions:
                    files.append(str(file_path))
        
        return sorted(files)


class NavigationDataLoader(DataLoader):
    """导航数据加载器"""
    
    def __init__(self, data_root: str, split_file: str, **kwargs):
        """
        初始化导航数据加载器
        
        Args:
            data_root: 数据根目录
            split_file: 数据分割文件路径
            **kwargs: 其他参数
        """
        super().__init__(data_root, **kwargs)
        self.split_file = split_file
        self._split_data = None
    
    def load_split_data(self) -> Dict[str, List[str]]:
        """
        加载数据分割信息
        
        Returns:
            Dict: 分割数据
        """
        if self._split_data is None:
            with ExceptionContext("Loading split data"):
                self._split_data = self.load_json(self.split_file)
                
                # 验证分割数据格式
                for split in DATASET_SPLITS:
                    if split not in self._split_data:
                        logger.warning(f"Split '{split}' not found in split file")
                        self._split_data[split] = []
                
                logger.info(f"Loaded split data with {len(self._split_data)} splits")
        
        return self._split_data
    
    def get_scene_list(self, split: str) -> List[str]:
        """
        获取指定分割的场景列表
        
        Args:
            split: 数据分割名称
            
        Returns:
            List[str]: 场景列表
        """
        split_data = self.load_split_data()
        
        if split not in split_data:
            raise DataProcessingError(
                f"Unknown split: {split}",
                details={"available_splits": list(split_data.keys())}
            )
        
        return split_data[split]
    
    def load_scene_data(self, scene_name: str) -> List[Dict[str, Any]]:
        """
        加载场景数据
        
        Args:
            scene_name: 场景名称
            
        Returns:
            List[Dict]: 场景数据列表
        """
        scene_files = []
        
        # 查找场景目录
        for dataset_type in ["HM3D", "Matterport"]:
            scene_dir = self.data_root / dataset_type / scene_name
            if scene_dir.exists():
                # 查找JSON文件
                json_files = self.list_files(scene_dir, [".json"])
                scene_files.extend(json_files)
        
        if not scene_files:
            logger.warning(f"No data files found for scene: {scene_name}")
            return []
        
        # 加载所有JSON文件
        scene_data = []
        for json_file in scene_files:
            try:
                data = self.load_json(json_file)
                if isinstance(data, list):
                    scene_data.extend(data)
                else:
                    scene_data.append(data)
            except Exception as e:
                logger.error(f"Failed to load scene file {json_file}: {e}")
                continue
        
        logger.debug(f"Loaded {len(scene_data)} samples from scene: {scene_name}")
        return scene_data
    
    def validate_sample(self, sample: Dict[str, Any], scene_dir: str) -> bool:
        """
        验证样本数据有效性
        
        Args:
            sample: 样本数据
            scene_dir: 场景目录
            
        Returns:
            bool: 是否有效
        """
        required_fields = ["question", "answer", "path"]
        
        # 检查必需字段
        for field in required_fields:
            if field not in sample:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # 验证路径数据
        path = sample.get("path", [])
        if not isinstance(path, list) or len(path) == 0:
            logger.warning("Invalid or empty path data")
            return False
        
        # 验证图像路径
        valid_images = 0
        for step in path:
            if not isinstance(step, dict):
                continue
            
            img_path = step.get("img", "")
            if img_path:
                full_img_path = os.path.join(scene_dir, img_path)
                if self.validate_image_path(full_img_path):
                    valid_images += 1
        
        if valid_images == 0:
            logger.warning("No valid images found in sample")
            return False
        
        return True
    
    def load_all_data(self, split: str) -> List[Dict[str, Any]]:
        """
        加载指定分割的所有数据
        
        Args:
            split: 数据分割名称
            
        Returns:
            List[Dict]: 所有样本数据
        """
        with ExceptionContext(f"Loading all data for split: {split}"):
            scene_list = self.get_scene_list(split)
            all_data = []
            
            for scene_name in scene_list:
                scene_data = self.load_scene_data(scene_name)
                
                # 为每个样本添加场景信息
                for sample in scene_data:
                    sample["scene_name"] = scene_name
                    all_data.append(sample)
            
            logger.info(f"Loaded {len(all_data)} samples for split: {split}")
            return all_data
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据集统计信息
        
        Returns:
            Dict: 统计信息
        """
        split_data = self.load_split_data()
        stats = {
            "total_scenes": 0,
            "splits": {}
        }
        
        for split, scenes in split_data.items():
            scene_count = len(scenes)
            stats["splits"][split] = {
                "scene_count": scene_count,
                "scenes": scenes
            }
            stats["total_scenes"] += scene_count
        
        return stats
