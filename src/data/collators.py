"""
数据整理器模块

负责批量数据的整理和填充
"""

import torch
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import logging

from ..core import get_logger, DataProcessingError
from ..core.constants import IGNORE_INDEX

logger = get_logger(__name__)


@dataclass
class CollatorConfig:
    """数据整理器配置"""
    
    padding: bool = True
    max_length: Optional[int] = None
    pad_to_multiple_of: Optional[int] = None
    return_tensors: str = "pt"
    label_pad_token_id: int = IGNORE_INDEX


class NavigationCollator:
    """导航数据整理器"""
    
    def __init__(
        self,
        tokenizer=None,
        config: Optional[CollatorConfig] = None,
        **kwargs
    ):
        """
        初始化数据整理器
        
        Args:
            tokenizer: 分词器
            config: 整理器配置
            **kwargs: 其他参数
        """
        self.tokenizer = tokenizer
        self.config = config or CollatorConfig(**kwargs)
    
    def __call__(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """
        整理批量数据
        
        Args:
            batch: 批量样本
            
        Returns:
            Dict[str, torch.Tensor]: 整理后的批量数据
        """
        try:
            return self._collate_batch(batch)
        except Exception as e:
            logger.error(f"Error in data collation: {e}")
            raise DataProcessingError(f"Data collation failed: {e}")
    
    def _collate_batch(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """内部批量整理方法"""
        if not batch:
            raise ValueError("Empty batch provided")
        
        # 检查批量数据的一致性
        first_sample = batch[0]
        
        # 如果有input_ids，说明已经tokenized
        if "input_ids" in first_sample:
            return self._collate_tokenized_batch(batch)
        
        # 如果有messages，需要先tokenize
        if "messages" in first_sample:
            return self._collate_messages_batch(batch)
        
        # 简单模式：直接整理tensor
        return self._collate_simple_batch(batch)
    
    def _collate_tokenized_batch(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """整理已tokenized的批量数据"""
        collated = {}
        
        # 收集所有键
        all_keys = set()
        for sample in batch:
            all_keys.update(sample.keys())
        
        for key in all_keys:
            values = []
            for sample in batch:
                if key in sample:
                    value = sample[key]
                    if isinstance(value, torch.Tensor):
                        values.append(value)
                    elif isinstance(value, (list, tuple)):
                        values.append(torch.tensor(value))
                    else:
                        # 跳过非tensor数据
                        continue
                else:
                    # 如果某个样本缺少这个键，跳过
                    continue
            
            if values:
                if key in ["input_ids", "attention_mask", "labels"]:
                    # 需要padding的序列数据
                    collated[key] = self._pad_sequences(values, key)
                elif key in ["pixel_values", "image_grid_thw"]:
                    # 图像相关数据
                    collated[key] = torch.stack(values, dim=0)
                else:
                    # 其他数据尝试stack
                    try:
                        collated[key] = torch.stack(values, dim=0)
                    except Exception:
                        # 如果无法stack，保持为列表
                        collated[key] = values
        
        return collated
    
    def _collate_messages_batch(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """整理messages格式的批量数据"""
        if not self.tokenizer:
            raise ValueError("Tokenizer required for messages format")
        
        # 提取messages
        messages_list = [sample["messages"] for sample in batch]
        
        # 使用tokenizer处理
        try:
            # 应用聊天模板
            texts = []
            for messages in messages_list:
                text = self.tokenizer.apply_chat_template(
                    messages, 
                    tokenize=False, 
                    add_generation_prompt=False
                )
                texts.append(text)
            
            # Tokenize
            tokenized = self.tokenizer(
                texts,
                padding=self.config.padding,
                max_length=self.config.max_length,
                truncation=True,
                return_tensors=self.config.return_tensors
            )
            
            # 处理图像数据
            if "image" in batch[0] or "pixel_values" in batch[0]:
                image_data = self._collate_image_data(batch)
                tokenized.update(image_data)
            
            return tokenized
            
        except Exception as e:
            logger.error(f"Error tokenizing messages: {e}")
            # 回退到简单整理
            return self._collate_simple_batch(batch)
    
    def _collate_simple_batch(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """简单批量整理"""
        collated = {}
        
        # 收集tensor数据
        for key in batch[0].keys():
            values = []
            for sample in batch:
                if key in sample:
                    value = sample[key]
                    if isinstance(value, torch.Tensor):
                        values.append(value)
                    elif isinstance(value, (int, float)):
                        values.append(torch.tensor(value))
                    elif isinstance(value, (list, tuple)) and all(isinstance(x, (int, float)) for x in value):
                        values.append(torch.tensor(value))
            
            if values:
                try:
                    collated[key] = torch.stack(values, dim=0)
                except Exception:
                    # 如果无法stack，保持为列表
                    collated[key] = values
        
        return collated
    
    def _collate_image_data(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """整理图像数据"""
        image_data = {}
        
        # 处理pixel_values
        if "pixel_values" in batch[0]:
            pixel_values = [sample["pixel_values"] for sample in batch]
            image_data["pixel_values"] = torch.stack(pixel_values, dim=0)
        
        # 处理image_grid_thw
        if "image_grid_thw" in batch[0]:
            grid_thw = [sample["image_grid_thw"] for sample in batch]
            image_data["image_grid_thw"] = torch.stack(grid_thw, dim=0)
        
        return image_data
    
    def _pad_sequences(self, sequences: List[torch.Tensor], key: str) -> torch.Tensor:
        """填充序列数据"""
        if not sequences:
            return torch.tensor([])
        
        # 确定填充值
        if key == "labels":
            pad_value = self.config.label_pad_token_id
        elif key == "attention_mask":
            pad_value = 0
        else:
            pad_value = self.tokenizer.pad_token_id if self.tokenizer else 0
        
        # 获取最大长度
        max_length = max(seq.size(-1) for seq in sequences)
        
        # 限制最大长度
        if self.config.max_length:
            max_length = min(max_length, self.config.max_length)
        
        # 填充到指定倍数
        if self.config.pad_to_multiple_of:
            max_length = ((max_length + self.config.pad_to_multiple_of - 1) 
                         // self.config.pad_to_multiple_of * self.config.pad_to_multiple_of)
        
        # 执行填充
        padded_sequences = []
        for seq in sequences:
            seq_len = seq.size(-1)
            if seq_len > max_length:
                # 截断
                padded_seq = seq[..., :max_length]
            elif seq_len < max_length:
                # 填充
                pad_size = max_length - seq_len
                if seq.dim() == 1:
                    padding = torch.full((pad_size,), pad_value, dtype=seq.dtype)
                    padded_seq = torch.cat([seq, padding], dim=0)
                else:
                    padding_shape = list(seq.shape)
                    padding_shape[-1] = pad_size
                    padding = torch.full(padding_shape, pad_value, dtype=seq.dtype)
                    padded_seq = torch.cat([seq, padding], dim=-1)
            else:
                padded_seq = seq
            
            padded_sequences.append(padded_seq)
        
        return torch.stack(padded_sequences, dim=0)


def create_collate_fn(
    tokenizer=None,
    padding: bool = True,
    max_length: Optional[int] = None,
    **kwargs
) -> NavigationCollator:
    """
    创建数据整理函数
    
    Args:
        tokenizer: 分词器
        padding: 是否填充
        max_length: 最大长度
        **kwargs: 其他参数
        
    Returns:
        NavigationCollator: 数据整理器
    """
    config = CollatorConfig(
        padding=padding,
        max_length=max_length,
        **kwargs
    )
    
    return NavigationCollator(tokenizer=tokenizer, config=config)


# 兼容性函数
def collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
    """
    简单的数据整理函数（向后兼容）
    
    Args:
        batch: 批量样本
        
    Returns:
        Dict[str, torch.Tensor]: 整理后的数据
    """
    collator = NavigationCollator()
    return collator(batch)
