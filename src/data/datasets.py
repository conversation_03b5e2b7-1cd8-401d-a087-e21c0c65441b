"""
数据集类模块

定义各种数据集类
"""

import os
from typing import List, Dict, Any, Optional, Callable
from torch.utils.data import Dataset
import logging

from ..core import get_logger, DataProcessingError, ExceptionContext
from .loaders import NavigationDataLoader
from .processors import NavigationProcessor

logger = get_logger(__name__)


class BaseDataset(Dataset):
    """基础数据集类"""
    
    def __init__(
        self,
        data: List[Dict[str, Any]],
        processor: Optional[Callable] = None,
        transform: Optional[Callable] = None
    ):
        """
        初始化基础数据集
        
        Args:
            data: 数据列表
            processor: 数据处理器
            transform: 数据变换函数
        """
        self.data = data
        self.processor = processor
        self.transform = transform
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data)
    
    def __getitem__(self, index: int) -> Dict[str, Any]:
        """获取单个样本"""
        if index >= len(self.data):
            raise IndexError(f"Index {index} out of range for dataset of size {len(self.data)}")
        
        sample = self.data[index]
        
        # 应用处理器
        if self.processor:
            sample = self.processor(sample)
        
        # 应用变换
        if self.transform:
            sample = self.transform(sample)
        
        return sample
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        return {
            "total_samples": len(self.data),
            "sample_keys": list(self.data[0].keys()) if self.data else [],
        }


class NavigationDataset(BaseDataset):
    """导航数据集类"""
    
    def __init__(
        self,
        data_root: str,
        split_file: str,
        split: str = "train",
        processor=None,
        max_length: int = 2048,
        image_size: tuple = (224, 224),
        max_samples: int = -1,
        validate_data: bool = True
    ):
        """
        初始化导航数据集
        
        Args:
            data_root: 数据根目录
            split_file: 数据分割文件
            split: 数据分割名称
            processor: 外部处理器
            max_length: 最大序列长度
            image_size: 图像尺寸
            max_samples: 最大样本数量，-1表示使用全部
            validate_data: 是否验证数据
        """
        self.data_root = data_root
        self.split_file = split_file
        self.split = split
        self.max_length = max_length
        self.image_size = image_size
        self.max_samples = max_samples
        self.validate_data = validate_data
        
        # 初始化加载器和处理器
        self.data_loader = NavigationDataLoader(data_root, split_file)
        self.data_processor = NavigationProcessor(
            processor=processor,
            image_size=image_size,
            max_length=max_length
        )
        
        # 加载数据
        self.samples = self._load_samples()
        
        # 初始化基类
        super().__init__(
            data=self.samples,
            processor=self.data_processor.process_sample
        )
        
        logger.info(f"NavigationDataset initialized with {len(self.samples)} samples")
    
    def _load_samples(self) -> List[Dict[str, Any]]:
        """加载所有样本"""
        with ExceptionContext(f"Loading samples for split: {self.split}"):
            scene_list = self.data_loader.get_scene_list(self.split)
            all_samples = []
            
            for scene_name in scene_list:
                scene_samples = self._load_scene_samples(scene_name)
                all_samples.extend(scene_samples)
                
                logger.debug(f"Loaded {len(scene_samples)} samples from scene: {scene_name}")
            
            # 限制样本数量
            if self.max_samples > 0 and len(all_samples) > self.max_samples:
                all_samples = all_samples[:self.max_samples]
                logger.info(f"Limited samples to {self.max_samples}")
            
            return all_samples
    
    def _load_scene_samples(self, scene_name: str) -> List[Dict[str, Any]]:
        """加载单个场景的样本"""
        scene_data = self.data_loader.load_scene_data(scene_name)
        if not scene_data:
            return []
        
        # 查找场景目录
        scene_dir = None
        for dataset_type in ["HM3D", "Matterport"]:
            potential_dir = os.path.join(self.data_root, dataset_type, scene_name)
            if os.path.exists(potential_dir):
                scene_dir = potential_dir
                break
        
        if not scene_dir:
            logger.warning(f"Scene directory not found for: {scene_name}")
            return []
        
        # 提取训练样本
        samples = self.data_processor.extract_samples_from_scene_data(
            scene_data, scene_dir
        )
        
        # 验证数据
        if self.validate_data:
            samples = self._validate_samples(samples)
        
        return samples
    
    def _validate_samples(self, samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证样本数据"""
        valid_samples = []
        
        for sample in samples:
            try:
                # 检查必需字段
                required_fields = ["image_path", "target", "question"]
                if all(field in sample for field in required_fields):
                    # 检查图像文件是否存在
                    if os.path.exists(sample["image_path"]):
                        valid_samples.append(sample)
                    else:
                        logger.warning(f"Image not found: {sample['image_path']}")
                else:
                    logger.warning(f"Missing required fields in sample")
            except Exception as e:
                logger.error(f"Error validating sample: {e}")
                continue
        
        logger.debug(f"Validated {len(valid_samples)}/{len(samples)} samples")
        return valid_samples
    
    def get_sample_by_action(self, action: str) -> List[Dict[str, Any]]:
        """
        根据动作类型获取样本
        
        Args:
            action: 动作类型
            
        Returns:
            List[Dict]: 匹配的样本列表
        """
        matching_samples = []
        for sample in self.samples:
            if sample.get("action") == action:
                matching_samples.append(sample)
        return matching_samples
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        base_stats = super().get_statistics()
        
        # 统计动作分布
        action_counts = {}
        for sample in self.samples:
            action = sample.get("action", "unknown")
            action_counts[action] = action_counts.get(action, 0) + 1
        
        # 统计场景分布
        scene_counts = {}
        for sample in self.samples:
            scene = sample.get("scene_name", "unknown")
            scene_counts[scene] = scene_counts.get(scene, 0) + 1
        
        navigation_stats = {
            "action_distribution": action_counts,
            "scene_distribution": scene_counts,
            "split": self.split,
            "data_root": self.data_root,
        }
        
        return {**base_stats, **navigation_stats}
    
    def save_processed_data(self, save_path: str):
        """
        保存预处理后的数据
        
        Args:
            save_path: 保存路径
        """
        import pickle
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        with open(save_path, 'wb') as f:
            pickle.dump(self.samples, f)
        
        logger.info(f"Processed data saved to: {save_path}")
    
    def load_processed_data(self, load_path: str):
        """
        加载预处理后的数据
        
        Args:
            load_path: 加载路径
        """
        import pickle
        
        if not os.path.exists(load_path):
            raise DataProcessingError(f"Processed data file not found: {load_path}")
        
        with open(load_path, 'rb') as f:
            self.samples = pickle.load(f)
        
        # 更新基类数据
        self.data = self.samples
        
        logger.info(f"Processed data loaded from: {load_path}")


def create_navigation_dataset(
    data_root: str,
    split_file: str,
    split: str = "train",
    **kwargs
) -> NavigationDataset:
    """
    创建导航数据集的工厂函数
    
    Args:
        data_root: 数据根目录
        split_file: 分割文件路径
        split: 数据分割名称
        **kwargs: 其他参数
        
    Returns:
        NavigationDataset: 导航数据集实例
    """
    return NavigationDataset(
        data_root=data_root,
        split_file=split_file,
        split=split,
        **kwargs
    )
