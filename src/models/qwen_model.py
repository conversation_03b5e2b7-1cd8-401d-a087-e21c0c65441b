"""
Qwen模型封装模块

专门处理Qwen系列视觉语言模型
"""

import torch
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

from ..core import get_logger, ModelLoadError, ExceptionContext
from .base import BaseModel, ModelFactory

logger = get_logger(__name__)


@dataclass
class QwenModelConfig:
    """Qwen模型配置"""
    
    # 基础配置
    model_name: str = "data/Qwen2.5-VL-3B-Instruct"
    device_map: str = "auto"
    torch_dtype: str = "bfloat16"
    trust_remote_code: bool = True
    
    # 量化配置
    use_4bit: bool = True
    bnb_4bit_use_double_quant: bool = True
    bnb_4bit_quant_type: str = "nf4"
    
    # 注意力配置
    attn_implementation: str = "eager"  # "eager", "flash_attention_2", "sdpa"
    use_cache: bool = False  # 训练时设为False
    
    # 性能配置
    low_cpu_mem_usage: bool = True
    gradient_checkpointing: bool = True
    
    def get_quantization_config(self):
        """获取量化配置"""
        if not self.use_4bit:
            return None
        
        try:
            from transformers import BitsAndBytesConfig
            
            return BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_use_double_quant=self.bnb_4bit_use_double_quant,
                bnb_4bit_quant_type=self.bnb_4bit_quant_type,
                bnb_4bit_compute_dtype=getattr(torch, self.torch_dtype),
            )
        except ImportError:
            logger.warning("BitsAndBytesConfig not available, skipping quantization")
            return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "model_name": self.model_name,
            "device_map": self.device_map,
            "torch_dtype": self.torch_dtype,
            "trust_remote_code": self.trust_remote_code,
            "use_4bit": self.use_4bit,
            "attn_implementation": self.attn_implementation,
            "use_cache": self.use_cache,
            "low_cpu_mem_usage": self.low_cpu_mem_usage,
            "gradient_checkpointing": self.gradient_checkpointing,
        }


class QwenVLModel(BaseModel):
    """Qwen视觉语言模型封装"""
    
    def __init__(
        self,
        model_name: str,
        config: Optional[QwenModelConfig] = None,
        **kwargs
    ):
        """
        初始化Qwen模型
        
        Args:
            model_name: 模型名称或路径
            config: 模型配置
            **kwargs: 其他参数
        """
        # 合并配置
        if config is None:
            config = QwenModelConfig(model_name=model_name, **kwargs)
        else:
            # 更新配置中的model_name
            config.model_name = model_name
            # 更新其他参数
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
        
        self.config = config
        
        # 初始化基类
        super().__init__(
            model_name=model_name,
            device_map=config.device_map,
            torch_dtype=config.torch_dtype,
            trust_remote_code=config.trust_remote_code
        )
    
    def load_model(self):
        """加载Qwen模型"""
        with ExceptionContext(f"Loading Qwen model: {self.model_name}"):
            try:
                from transformers import Qwen2_5_VLForConditionalGeneration
                
                # 准备加载参数
                load_kwargs = {
                    "torch_dtype": self.torch_dtype,
                    "device_map": self.device_map,
                    "trust_remote_code": self.trust_remote_code,
                    "low_cpu_mem_usage": self.config.low_cpu_mem_usage,
                    "attn_implementation": self.config.attn_implementation,
                }
                
                # 添加量化配置
                quantization_config = self.config.get_quantization_config()
                if quantization_config:
                    load_kwargs["quantization_config"] = quantization_config
                
                # 加载模型
                model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                    self.model_name,
                    **load_kwargs
                )
                
                # 配置模型
                model.config.use_cache = self.config.use_cache
                
                # 启用梯度检查点
                if self.config.gradient_checkpointing:
                    model.gradient_checkpointing_enable()
                
                logger.info(f"Qwen model loaded successfully: {self.model_name}")
                return model
                
            except ImportError as e:
                raise ModelLoadError(
                    f"Failed to import Qwen model: {e}",
                    model_path=self.model_name
                )
            except Exception as e:
                raise ModelLoadError(
                    f"Failed to load Qwen model: {e}",
                    model_path=self.model_name,
                    cause=e
                )
    
    def load_tokenizer(self):
        """加载分词器"""
        with ExceptionContext(f"Loading tokenizer: {self.model_name}"):
            try:
                from transformers import AutoTokenizer
                
                tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    trust_remote_code=self.trust_remote_code
                )
                
                logger.info(f"Tokenizer loaded successfully: {self.model_name}")
                return tokenizer
                
            except Exception as e:
                raise ModelLoadError(
                    f"Failed to load tokenizer: {e}",
                    model_path=self.model_name,
                    cause=e
                )
    
    def load_processor(self):
        """加载处理器"""
        with ExceptionContext(f"Loading processor: {self.model_name}"):
            try:
                from transformers import AutoProcessor
                
                processor = AutoProcessor.from_pretrained(
                    self.model_name,
                    trust_remote_code=self.trust_remote_code
                )
                
                logger.info(f"Processor loaded successfully: {self.model_name}")
                return processor
                
            except Exception as e:
                raise ModelLoadError(
                    f"Failed to load processor: {e}",
                    model_path=self.model_name,
                    cause=e
                )
    
    def prepare_for_training(self):
        """为训练准备模型"""
        if not self._is_loaded:
            self.load_all()
        
        # 禁用缓存
        self._model.config.use_cache = False
        
        # 启用梯度检查点
        if self.config.gradient_checkpointing:
            self._model.gradient_checkpointing_enable()
        
        logger.info("Model prepared for training")
    
    def prepare_for_inference(self):
        """为推理准备模型"""
        if not self._is_loaded:
            self.load_all()
        
        # 启用缓存
        self._model.config.use_cache = True
        
        # 设置为评估模式
        self._model.eval()
        
        logger.info("Model prepared for inference")
    
    def get_trainable_parameters(self) -> Dict[str, Any]:
        """获取可训练参数信息"""
        if not self._is_loaded:
            return {}
        
        total_params = sum(p.numel() for p in self._model.parameters())
        trainable_params = sum(p.numel() for p in self._model.parameters() if p.requires_grad)
        
        return {
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "trainable_percentage": (trainable_params / total_params) * 100 if total_params > 0 else 0,
        }
    
    def print_trainable_parameters(self):
        """打印可训练参数信息"""
        params_info = self.get_trainable_parameters()
        
        if params_info:
            logger.info(
                f"Trainable parameters: {params_info['trainable_parameters']:,} / "
                f"{params_info['total_parameters']:,} "
                f"({params_info['trainable_percentage']:.2f}%)"
            )


# 注册Qwen模型到工厂
ModelFactory.register_model("qwen_vl", QwenVLModel)
ModelFactory.register_model("qwen2_vl", QwenVLModel)
ModelFactory.register_model("qwen2_5_vl", QwenVLModel)


def create_qwen_model(
    model_name: str,
    **kwargs
) -> QwenVLModel:
    """
    创建Qwen模型的便捷函数
    
    Args:
        model_name: 模型名称
        **kwargs: 其他参数
        
    Returns:
        QwenVLModel: Qwen模型实例
    """
    return QwenVLModel(model_name=model_name, **kwargs)
