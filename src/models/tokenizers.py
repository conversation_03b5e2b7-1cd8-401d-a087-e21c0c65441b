"""
分词器管理模块

负责分词器的加载、配置和特殊Token处理
"""

import torch
from typing import Dict, List, Optional, Any
import logging

from ..core import get_logger, ModelLoadError, ExceptionContext
from ..core.constants import NAVIGATION_TOKENS, ACTION_TOKEN_MAP, TOKEN_ACTION_MAP

logger = get_logger(__name__)


class NavigationTokenizer:
    """导航任务专用分词器"""
    
    def __init__(self, base_tokenizer_name: str = "Qwen/Qwen2.5-VL-3B-Instruct"):
        """
        初始化导航分词器
        
        Args:
            base_tokenizer_name: 基础分词器名称
        """
        self.base_tokenizer_name = base_tokenizer_name
        self.tokenizer = None
        self.processor = None
        self.original_vocab_size = None
        self.new_vocab_size = None
        self.navigation_token_ids = {}
        
        self._setup_tokenizer()
    
    def _setup_tokenizer(self):
        """设置分词器并添加特殊Token"""
        with ExceptionContext("Setting up navigation tokenizer"):
            try:
                from transformers import AutoTokenizer
                
                # 加载基础分词器
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.base_tokenizer_name, 
                    trust_remote_code=True
                )
                
                # 记录原始词汇表大小
                self.original_vocab_size = len(self.tokenizer)
                
                # 添加特殊Token
                self._add_navigation_tokens()
                
                # 更新词汇表大小
                self.new_vocab_size = len(self.tokenizer)
                
                logger.info(
                    f"Navigation tokenizer setup completed. "
                    f"Vocab size: {self.original_vocab_size} -> {self.new_vocab_size}"
                )
                
            except Exception as e:
                raise ModelLoadError(f"Failed to setup navigation tokenizer: {e}", cause=e)
    
    def _add_navigation_tokens(self):
        """添加导航特殊Token"""
        special_tokens_dict = {"additional_special_tokens": NAVIGATION_TOKENS}
        
        num_added_tokens = self.tokenizer.add_special_tokens(special_tokens_dict)
        
        # 获取Token ID映射
        for token in NAVIGATION_TOKENS:
            token_id = self.tokenizer.convert_tokens_to_ids(token)
            self.navigation_token_ids[token] = token_id
            logger.debug(f"Added navigation token: {token} -> ID: {token_id}")
        
        logger.info(f"Added {num_added_tokens} navigation tokens")
    
    def setup_processor(self):
        """设置处理器（用于视觉-语言模型）"""
        with ExceptionContext("Setting up processor"):
            try:
                from transformers import AutoProcessor
                
                self.processor = AutoProcessor.from_pretrained(
                    self.base_tokenizer_name, 
                    trust_remote_code=True
                )
                
                # 更新处理器的分词器
                self.processor.tokenizer = self.tokenizer
                
                logger.info("Processor setup completed")
                return self.processor
                
            except Exception as e:
                raise ModelLoadError(f"Failed to setup processor: {e}", cause=e)
    
    def get_navigation_token_ids(self) -> Dict[str, int]:
        """获取导航Token的ID映射"""
        return self.navigation_token_ids.copy()
    
    def encode_navigation_action(self, action: str) -> Optional[int]:
        """
        编码导航动作为Token ID
        
        Args:
            action: 动作字符串
            
        Returns:
            Optional[int]: 对应的Token ID
        """
        if action in ACTION_TOKEN_MAP:
            token = ACTION_TOKEN_MAP[action]
            return self.navigation_token_ids.get(token)
        else:
            logger.warning(f"Unknown navigation action: {action}")
            return None
    
    def decode_navigation_action(self, token_id: int) -> Optional[str]:
        """
        解码Token ID为导航动作
        
        Args:
            token_id: Token ID
            
        Returns:
            Optional[str]: 动作字符串
        """
        token = self.tokenizer.convert_ids_to_tokens(token_id)
        return TOKEN_ACTION_MAP.get(token)
    
    def is_navigation_token(self, token_id: int) -> bool:
        """
        检查Token ID是否为导航Token
        
        Args:
            token_id: Token ID
            
        Returns:
            bool: 是否为导航Token
        """
        token = self.tokenizer.convert_ids_to_tokens(token_id)
        return token in NAVIGATION_TOKENS
    
    def resize_model_embeddings(self, model):
        """
        调整模型的embedding层大小以适应新的词汇表
        
        Args:
            model: 要调整的模型
            
        Returns:
            调整后的模型
        """
        if self.new_vocab_size > self.original_vocab_size:
            with ExceptionContext("Resizing model embeddings"):
                logger.info(
                    f"Resizing model embeddings from {self.original_vocab_size} "
                    f"to {self.new_vocab_size}"
                )
                
                model.resize_token_embeddings(self.new_vocab_size)
                
                # 更新模型配置中的vocab_size
                model.config.vocab_size = self.new_vocab_size
                
                # 初始化新Token的embedding
                self._initialize_new_embeddings(model)
                
                logger.info("Model embeddings resized and initialized")
        
        return model
    
    def _initialize_new_embeddings(self, model):
        """初始化新Token的embedding"""
        with torch.no_grad():
            # 获取新添加的Token的embedding
            new_embeddings = model.get_input_embeddings().weight[
                -len(NAVIGATION_TOKENS):
            ]
            
            # 使用现有Token的平均值初始化新Token
            existing_embeddings = model.get_input_embeddings().weight[
                :-len(NAVIGATION_TOKENS)
            ]
            mean_embedding = existing_embeddings.mean(dim=0)
            
            for i in range(len(NAVIGATION_TOKENS)):
                # 添加小的随机扰动
                new_embeddings[i] = (
                    mean_embedding + torch.randn_like(mean_embedding) * 0.02
                )
    
    def get_tokenizer(self):
        """获取分词器"""
        return self.tokenizer
    
    def get_processor(self):
        """获取处理器"""
        if self.processor is None:
            self.setup_processor()
        return self.processor
    
    def get_vocab_info(self) -> Dict[str, Any]:
        """获取词汇表信息"""
        return {
            "original_vocab_size": self.original_vocab_size,
            "new_vocab_size": self.new_vocab_size,
            "added_tokens": len(NAVIGATION_TOKENS),
            "navigation_tokens": NAVIGATION_TOKENS,
            "navigation_token_ids": self.navigation_token_ids,
        }


class TokenizerManager:
    """分词器管理器"""
    
    def __init__(self):
        """初始化分词器管理器"""
        self._tokenizers = {}
        self._processors = {}
    
    def get_navigation_tokenizer(
        self, 
        model_name: str,
        force_reload: bool = False
    ) -> NavigationTokenizer:
        """
        获取导航分词器
        
        Args:
            model_name: 模型名称
            force_reload: 是否强制重新加载
            
        Returns:
            NavigationTokenizer: 导航分词器
        """
        if force_reload or model_name not in self._tokenizers:
            self._tokenizers[model_name] = NavigationTokenizer(model_name)
        
        return self._tokenizers[model_name]
    
    def get_standard_tokenizer(self, model_name: str):
        """
        获取标准分词器
        
        Args:
            model_name: 模型名称
            
        Returns:
            标准分词器
        """
        try:
            from transformers import AutoTokenizer
            
            if model_name not in self._tokenizers:
                tokenizer = AutoTokenizer.from_pretrained(
                    model_name, 
                    trust_remote_code=True
                )
                self._tokenizers[model_name] = tokenizer
            
            return self._tokenizers[model_name]
            
        except Exception as e:
            raise ModelLoadError(f"Failed to load standard tokenizer: {e}", cause=e)
    
    def get_processor(self, model_name: str):
        """
        获取处理器
        
        Args:
            model_name: 模型名称
            
        Returns:
            处理器
        """
        try:
            from transformers import AutoProcessor
            
            if model_name not in self._processors:
                processor = AutoProcessor.from_pretrained(
                    model_name, 
                    trust_remote_code=True
                )
                self._processors[model_name] = processor
            
            return self._processors[model_name]
            
        except Exception as e:
            raise ModelLoadError(f"Failed to load processor: {e}", cause=e)
    
    def clear_cache(self):
        """清理缓存"""
        self._tokenizers.clear()
        self._processors.clear()
        logger.info("Tokenizer cache cleared")


# 全局分词器管理器
tokenizer_manager = TokenizerManager()


def get_navigation_tokenizer(model_name: str) -> NavigationTokenizer:
    """获取导航分词器的便捷函数"""
    return tokenizer_manager.get_navigation_tokenizer(model_name)


def get_standard_tokenizer(model_name: str):
    """获取标准分词器的便捷函数"""
    return tokenizer_manager.get_standard_tokenizer(model_name)


def get_processor(model_name: str):
    """获取处理器的便捷函数"""
    return tokenizer_manager.get_processor(model_name)
