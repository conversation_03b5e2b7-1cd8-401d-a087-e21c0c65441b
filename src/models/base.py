"""
基础模型类模块

定义模型的基础接口和工厂模式
"""

import torch
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, Tuple
import logging

from ..core import get_logger, ModelLoadError, ExceptionContext
from ..core.constants import ModelType, DeviceType, PrecisionType

logger = get_logger(__name__)


class BaseModel(ABC):
    """基础模型抽象类"""
    
    def __init__(
        self,
        model_name: str,
        device_map: str = "auto",
        torch_dtype: Union[str, torch.dtype] = torch.bfloat16,
        trust_remote_code: bool = True
    ):
        """
        初始化基础模型
        
        Args:
            model_name: 模型名称或路径
            device_map: 设备映射
            torch_dtype: 数据类型
            trust_remote_code: 是否信任远程代码
        """
        self.model_name = model_name
        self.device_map = device_map
        self.torch_dtype = self._parse_dtype(torch_dtype)
        self.trust_remote_code = trust_remote_code
        
        self._model = None
        self._tokenizer = None
        self._processor = None
        self._is_loaded = False
    
    @abstractmethod
    def load_model(self) -> Any:
        """加载模型"""
        pass
    
    @abstractmethod
    def load_tokenizer(self) -> Any:
        """加载分词器"""
        pass
    
    @abstractmethod
    def load_processor(self) -> Any:
        """加载处理器"""
        pass
    
    def _parse_dtype(self, dtype: Union[str, torch.dtype]) -> torch.dtype:
        """解析数据类型"""
        if isinstance(dtype, torch.dtype):
            return dtype
        
        dtype_map = {
            "float32": torch.float32,
            "float16": torch.float16,
            "bfloat16": torch.bfloat16,
            "int8": torch.int8,
            "int4": torch.int8,  # 近似映射
        }
        
        return dtype_map.get(dtype, torch.bfloat16)
    
    def get_model(self) -> Any:
        """获取模型"""
        if not self._is_loaded:
            self.load_all()
        return self._model
    
    def get_tokenizer(self) -> Any:
        """获取分词器"""
        if not self._is_loaded:
            self.load_all()
        return self._tokenizer
    
    def get_processor(self) -> Any:
        """获取处理器"""
        if not self._is_loaded:
            self.load_all()
        return self._processor
    
    def load_all(self):
        """加载所有组件"""
        with ExceptionContext("Loading all model components"):
            if not self._is_loaded:
                self._model = self.load_model()
                self._tokenizer = self.load_tokenizer()
                self._processor = self.load_processor()
                self._is_loaded = True
                logger.info(f"All model components loaded successfully: {self.model_name}")
    
    def unload(self):
        """卸载模型"""
        if self._model is not None:
            del self._model
            self._model = None
        
        if self._tokenizer is not None:
            del self._tokenizer
            self._tokenizer = None
        
        if self._processor is not None:
            del self._processor
            self._processor = None
        
        self._is_loaded = False
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("Model unloaded and memory cleared")
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "model_name": self.model_name,
            "device_map": self.device_map,
            "torch_dtype": str(self.torch_dtype),
            "trust_remote_code": self.trust_remote_code,
            "is_loaded": self._is_loaded,
        }
        
        if self._model is not None:
            try:
                info["model_type"] = type(self._model).__name__
                if hasattr(self._model, "config"):
                    info["model_config"] = {
                        "vocab_size": getattr(self._model.config, "vocab_size", None),
                        "hidden_size": getattr(self._model.config, "hidden_size", None),
                        "num_layers": getattr(self._model.config, "num_hidden_layers", None),
                    }
            except Exception as e:
                logger.warning(f"Error getting model info: {e}")
        
        return info
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        memory_info = {}
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                memory_info[f"gpu_{i}"] = {
                    "allocated": torch.cuda.memory_allocated(i) / 1024**3,  # GB
                    "cached": torch.cuda.memory_reserved(i) / 1024**3,  # GB
                    "total": torch.cuda.get_device_properties(i).total_memory / 1024**3,  # GB
                }
        
        return memory_info
    
    def save_model(self, save_path: str):
        """保存模型"""
        if not self._is_loaded:
            raise ModelLoadError("Model not loaded, cannot save")
        
        with ExceptionContext(f"Saving model to: {save_path}"):
            import os
            os.makedirs(save_path, exist_ok=True)
            
            # 保存模型
            if self._model is not None:
                self._model.save_pretrained(save_path)
            
            # 保存分词器
            if self._tokenizer is not None:
                self._tokenizer.save_pretrained(save_path)
            
            # 保存处理器
            if self._processor is not None:
                self._processor.save_pretrained(save_path)
            
            logger.info(f"Model saved to: {save_path}")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.load_all()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.unload()


class ModelFactory:
    """模型工厂类"""
    
    _model_registry = {}
    
    @classmethod
    def register_model(cls, model_type: str, model_class: type):
        """
        注册模型类
        
        Args:
            model_type: 模型类型
            model_class: 模型类
        """
        cls._model_registry[model_type] = model_class
        logger.debug(f"Registered model type: {model_type}")
    
    @classmethod
    def create_model(
        cls,
        model_type: str,
        model_name: str,
        **kwargs
    ) -> BaseModel:
        """
        创建模型实例
        
        Args:
            model_type: 模型类型
            model_name: 模型名称
            **kwargs: 其他参数
            
        Returns:
            BaseModel: 模型实例
        """
        if model_type not in cls._model_registry:
            raise ModelLoadError(
                f"Unknown model type: {model_type}",
                details={"available_types": list(cls._model_registry.keys())}
            )
        
        model_class = cls._model_registry[model_type]
        return model_class(model_name=model_name, **kwargs)
    
    @classmethod
    def get_available_models(cls) -> list:
        """获取可用的模型类型"""
        return list(cls._model_registry.keys())
    
    @classmethod
    def auto_detect_model_type(cls, model_name: str) -> str:
        """
        自动检测模型类型
        
        Args:
            model_name: 模型名称
            
        Returns:
            str: 模型类型
        """
        model_name_lower = model_name.lower()
        
        if "qwen2.5-vl" in model_name_lower or "qwen2_5_vl" in model_name_lower:
            return "qwen2_5_vl"
        elif "qwen2-vl" in model_name_lower or "qwen2_vl" in model_name_lower:
            return "qwen2_vl"
        elif "qwen-vl" in model_name_lower or "qwen_vl" in model_name_lower:
            return "qwen_vl"
        else:
            # 默认使用最新版本
            return "qwen2_5_vl"


def create_model(
    model_name: str,
    model_type: Optional[str] = None,
    **kwargs
) -> BaseModel:
    """
    创建模型的便捷函数
    
    Args:
        model_name: 模型名称
        model_type: 模型类型，如果为None则自动检测
        **kwargs: 其他参数
        
    Returns:
        BaseModel: 模型实例
    """
    if model_type is None:
        model_type = ModelFactory.auto_detect_model_type(model_name)
    
    return ModelFactory.create_model(
        model_type=model_type,
        model_name=model_name,
        **kwargs
    )
