"""
LoRA配置管理模块

负责LoRA微调的配置和应用
"""

import torch
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
import logging

from ..core import get_logger, ModelLoadError, ExceptionContext
from ..core.constants import (
    DEFAULT_LORA_RANK,
    DEFAULT_LORA_ALPHA,
    DEFAULT_LORA_DROPOUT,
    DEFAULT_LORA_TARGET_MODULES
)

logger = get_logger(__name__)


@dataclass
class LoRAConfig:
    """LoRA配置类"""
    
    # 基础LoRA参数
    r: int = DEFAULT_LORA_RANK  # LoRA rank
    lora_alpha: int = DEFAULT_LORA_ALPHA  # LoRA alpha
    lora_dropout: float = DEFAULT_LORA_DROPOUT  # LoRA dropout
    
    # 目标模块
    target_modules: List[str] = None
    
    # 高级配置
    bias: str = "none"  # "none", "all", "lora_only"
    task_type: str = "CAUSAL_LM"
    inference_mode: bool = False
    
    # 初始化配置
    init_lora_weights: bool = True
    lora_alpha_pattern: Optional[Dict[str, float]] = None
    lora_dropout_pattern: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        """后处理初始化"""
        if self.target_modules is None:
            self.target_modules = DEFAULT_LORA_TARGET_MODULES.copy()
    
    def get_peft_config(self):
        """获取PEFT配置"""
        try:
            from peft import LoraConfig, TaskType
            
            # 转换任务类型
            task_type = getattr(TaskType, self.task_type, TaskType.CAUSAL_LM)
            
            return LoraConfig(
                task_type=task_type,
                r=self.r,
                lora_alpha=self.lora_alpha,
                lora_dropout=self.lora_dropout,
                target_modules=self.target_modules,
                bias=self.bias,
                inference_mode=self.inference_mode,
                init_lora_weights=self.init_lora_weights,
            )
        except ImportError:
            raise ModelLoadError("PEFT library not available for LoRA configuration")
    
    def validate(self) -> bool:
        """验证配置有效性"""
        errors = []
        
        if self.r <= 0:
            errors.append("LoRA rank (r) must be positive")
        
        if self.lora_alpha <= 0:
            errors.append("LoRA alpha must be positive")
        
        if not (0 <= self.lora_dropout <= 1):
            errors.append("LoRA dropout must be between 0 and 1")
        
        if not self.target_modules:
            errors.append("Target modules cannot be empty")
        
        if self.bias not in ["none", "all", "lora_only"]:
            errors.append("Bias must be one of: none, all, lora_only")
        
        if errors:
            for error in errors:
                logger.error(f"LoRA config validation error: {error}")
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "r": self.r,
            "lora_alpha": self.lora_alpha,
            "lora_dropout": self.lora_dropout,
            "target_modules": self.target_modules,
            "bias": self.bias,
            "task_type": self.task_type,
            "inference_mode": self.inference_mode,
            "init_lora_weights": self.init_lora_weights,
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "LoRAConfig":
        """从字典创建配置"""
        return cls(**config_dict)


class LoRAManager:
    """LoRA管理器"""
    
    def __init__(self, config: Optional[LoRAConfig] = None):
        """
        初始化LoRA管理器
        
        Args:
            config: LoRA配置
        """
        self.config = config or LoRAConfig()
        self._peft_model = None
    
    def apply_lora(self, model):
        """
        应用LoRA到模型
        
        Args:
            model: 基础模型
            
        Returns:
            PEFT模型
        """
        with ExceptionContext("Applying LoRA to model"):
            if not self.config.validate():
                raise ModelLoadError("Invalid LoRA configuration")
            
            try:
                from peft import get_peft_model
                
                # 获取PEFT配置
                peft_config = self.config.get_peft_config()
                
                # 应用LoRA
                peft_model = get_peft_model(model, peft_config)
                
                self._peft_model = peft_model
                
                # 打印可训练参数信息
                self.print_trainable_parameters()
                
                logger.info("LoRA applied successfully")
                return peft_model
                
            except ImportError:
                raise ModelLoadError("PEFT library not available")
            except Exception as e:
                raise ModelLoadError(f"Failed to apply LoRA: {e}", cause=e)
    
    def find_target_modules(self, model) -> List[str]:
        """
        自动查找目标模块
        
        Args:
            model: 模型
            
        Returns:
            List[str]: 目标模块列表
        """
        target_modules = set()
        
        for name, module in model.named_modules():
            if isinstance(module, torch.nn.Linear):
                module_name = name.split(".")[-1]
                target_modules.add(module_name)
        
        # 过滤常见的线性层
        common_targets = [
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj",
            "fc1", "fc2", "dense", "linear"
        ]
        
        found_targets = [t for t in target_modules if t in common_targets]
        
        logger.info(f"Found target modules: {found_targets}")
        return found_targets
    
    def auto_configure_target_modules(self, model):
        """
        自动配置目标模块
        
        Args:
            model: 模型
        """
        target_modules = self.find_target_modules(model)
        if target_modules:
            self.config.target_modules = target_modules
            logger.info(f"Auto-configured target modules: {target_modules}")
        else:
            logger.warning("No suitable target modules found, using default")
    
    def get_trainable_parameters(self) -> Dict[str, Any]:
        """获取可训练参数信息"""
        if self._peft_model is None:
            return {}
        
        total_params = sum(p.numel() for p in self._peft_model.parameters())
        trainable_params = sum(p.numel() for p in self._peft_model.parameters() if p.requires_grad)
        
        return {
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "trainable_percentage": (trainable_params / total_params) * 100 if total_params > 0 else 0,
        }
    
    def print_trainable_parameters(self):
        """打印可训练参数信息"""
        if self._peft_model is not None and hasattr(self._peft_model, 'print_trainable_parameters'):
            self._peft_model.print_trainable_parameters()
        else:
            params_info = self.get_trainable_parameters()
            if params_info:
                logger.info(
                    f"Trainable parameters: {params_info['trainable_parameters']:,} / "
                    f"{params_info['total_parameters']:,} "
                    f"({params_info['trainable_percentage']:.2f}%)"
                )
    
    def save_lora_weights(self, save_path: str):
        """
        保存LoRA权重
        
        Args:
            save_path: 保存路径
        """
        if self._peft_model is None:
            raise ModelLoadError("No PEFT model to save")
        
        with ExceptionContext(f"Saving LoRA weights to: {save_path}"):
            import os
            os.makedirs(save_path, exist_ok=True)
            
            self._peft_model.save_pretrained(save_path)
            logger.info(f"LoRA weights saved to: {save_path}")
    
    def load_lora_weights(self, model, load_path: str):
        """
        加载LoRA权重
        
        Args:
            model: 基础模型
            load_path: 加载路径
            
        Returns:
            PEFT模型
        """
        with ExceptionContext(f"Loading LoRA weights from: {load_path}"):
            try:
                from peft import PeftModel
                
                peft_model = PeftModel.from_pretrained(model, load_path)
                self._peft_model = peft_model
                
                logger.info(f"LoRA weights loaded from: {load_path}")
                return peft_model
                
            except Exception as e:
                raise ModelLoadError(f"Failed to load LoRA weights: {e}", cause=e)
    
    def merge_and_unload(self):
        """
        合并LoRA权重并卸载
        
        Returns:
            合并后的模型
        """
        if self._peft_model is None:
            raise ModelLoadError("No PEFT model to merge")
        
        with ExceptionContext("Merging LoRA weights"):
            merged_model = self._peft_model.merge_and_unload()
            logger.info("LoRA weights merged successfully")
            return merged_model


def create_lora_config(
    r: int = DEFAULT_LORA_RANK,
    lora_alpha: int = DEFAULT_LORA_ALPHA,
    lora_dropout: float = DEFAULT_LORA_DROPOUT,
    target_modules: Optional[List[str]] = None,
    **kwargs
) -> LoRAConfig:
    """
    创建LoRA配置的便捷函数
    
    Args:
        r: LoRA rank
        lora_alpha: LoRA alpha
        lora_dropout: LoRA dropout
        target_modules: 目标模块
        **kwargs: 其他参数
        
    Returns:
        LoRAConfig: LoRA配置
    """
    return LoRAConfig(
        r=r,
        lora_alpha=lora_alpha,
        lora_dropout=lora_dropout,
        target_modules=target_modules,
        **kwargs
    )


def apply_lora_to_model(
    model,
    config: Optional[LoRAConfig] = None,
    **kwargs
):
    """
    应用LoRA到模型的便捷函数
    
    Args:
        model: 基础模型
        config: LoRA配置
        **kwargs: 其他配置参数
        
    Returns:
        PEFT模型
    """
    if config is None:
        config = create_lora_config(**kwargs)
    
    manager = LoRAManager(config)
    return manager.apply_lora(model)
