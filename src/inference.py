"""
推理和测试模块
实现模型推理功能，支持给定图像和问题输出导航动作或答案文本
按照Qwen2.5-VL官方格式规范实现
"""

import torch
from PIL import Image
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
import logging
import os
import json
from pathlib import Path

# 尝试导入官方工具
try:
    from qwen_vl_utils import process_vision_info

    QWEN_VL_UTILS_AVAILABLE = True
except ImportError:
    QWEN_VL_UTILS_AVAILABLE = False

try:
    from .model_config import NavigationModel, ModelConfig
    from .token_utils import NavigationTokenizer
except ImportError:
    from model_config import NavigationModel, ModelConfig
    from token_utils import NavigationTokenizer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NavigationInference:
    """导航推理类"""

    def __init__(
        self,
        model_path: str,
        model_config: Optional[ModelConfig] = None,
        device: str = "auto",
    ):
        """
        初始化推理器

        Args:
            model_path: 模型路径
            model_config: 模型配置
            device: 设备
        """
        self.model_path = model_path
        self.device = (
            device
            if device != "auto"
            else ("cuda" if torch.cuda.is_available() else "cpu")
        )

        # 使用默认配置如果没有提供
        if model_config is None:
            model_config = ModelConfig(
                model_name=model_path,
                use_4bit=False,  # 推理时通常不使用量化
                use_lora=True,
            )

        self.model_config = model_config
        self.nav_model = None
        self.model = None
        self.processor = None
        self.tokenizer = None
        self.nav_tokenizer = None

        self._load_model()

    def _load_model(self):
        """加载模型"""
        logger.info(f"Loading model from: {self.model_path}")

        try:
            # 初始化导航模型
            self.nav_model = NavigationModel(self.model_config)

            # 如果模型路径不同于配置中的路径，加载微调后的权重
            if self.model_path != self.model_config.model_name:
                self.nav_model.load_model(self.model_path)

            self.model = self.nav_model.get_model()
            self.processor = self.nav_model.get_processor()
            self.tokenizer = self.nav_model.get_tokenizer()
            self.nav_tokenizer = self.nav_model.get_navigation_tokenizer()

            # 设置为评估模式
            self.model.eval()

            logger.info("Model loaded successfully")

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

    def predict(
        self,
        image: Union[str, Image.Image, np.ndarray],
        question: str,
        max_new_tokens: int = 50,
        do_sample: bool = False,
        temperature: float = 1.0,
        top_p: float = 1.0,
        return_full_text: bool = False,
    ) -> Dict[str, Any]:
        """
        进行预测，使用官方格式

        Args:
            image: 输入图像（路径、PIL图像或numpy数组）
            question: 问题文本
            max_new_tokens: 最大新token数
            do_sample: 是否采样
            temperature: 温度参数
            top_p: top-p参数
            return_full_text: 是否返回完整文本

        Returns:
            prediction: 预测结果字典
        """
        # 处理图像输入
        if isinstance(image, str):
            image_path = image
            image = Image.open(image).convert("RGB")
        elif isinstance(image, np.ndarray):
            image_path = "numpy_array"
            image = Image.fromarray(image).convert("RGB")
        elif isinstance(image, Image.Image):
            image_path = "pil_image"
        else:
            raise ValueError("Image must be a file path, PIL Image, or numpy array")

        # 使用官方messages格式
        if QWEN_VL_UTILS_AVAILABLE and self.processor:
            try:
                # 构建官方格式的messages
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "image": (
                                    image_path if isinstance(image_path, str) else image
                                ),
                            },
                            {"type": "text", "text": question},
                        ],
                    }
                ]

                # 使用官方的apply_chat_template
                text = self.processor.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )

                # 使用官方的process_vision_info处理视觉信息
                image_inputs, video_inputs = process_vision_info(messages)

                # 使用processor处理
                inputs = self.processor(
                    text=[text],
                    images=image_inputs,
                    videos=video_inputs,
                    padding=True,
                    return_tensors="pt",
                )

            except Exception as e:
                logger.warning(f"Failed to use official format, falling back: {e}")
                # 回退到简单格式
                inputs = self.processor(
                    text=question, images=image, return_tensors="pt"
                )
        else:
            # 简单格式处理
            inputs = self.processor(text=question, images=image, return_tensors="pt")

        # 移动到设备
        inputs = {
            k: v.to(self.device) if v is not None else None for k, v in inputs.items()
        }
        inputs = {k: v for k, v in inputs.items() if v is not None}

        # 生成预测
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_new_tokens,
                do_sample=do_sample,
                temperature=temperature,
                top_p=top_p,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )

        # 解码输出
        if return_full_text:
            generated_text = self.tokenizer.decode(
                outputs[0], skip_special_tokens=False
            )
        else:
            # 只返回新生成的部分
            new_tokens = outputs[0][inputs["input_ids"].shape[1] :]
            generated_text = self.tokenizer.decode(
                new_tokens, skip_special_tokens=False
            )

        # 解析预测结果
        prediction = self._parse_prediction(generated_text, question)

        return prediction

    def _parse_prediction(self, generated_text: str, question: str) -> Dict[str, Any]:
        """
        解析预测结果

        Args:
            generated_text: 生成的文本
            question: 原始问题

        Returns:
            parsed_result: 解析后的结果
        """
        result = {
            "raw_output": generated_text.strip(),
            "question": question,
            "action": None,
            "answer": None,
            "is_navigation": False,
        }

        # 检查是否包含导航动作
        navigation_actions = ["<move_forward>", "<turn_left>", "<turn_right>"]

        for action_token in navigation_actions:
            if action_token in generated_text:
                action_name = action_token.strip("<>")
                result["action"] = action_name
                result["is_navigation"] = True
                result["answer"] = action_token
                break

        # 如果不是导航动作，则作为文本答案处理
        if not result["is_navigation"]:
            # 清理生成的文本
            answer = generated_text.strip()
            # 移除可能的特殊token
            for token in navigation_actions:
                answer = answer.replace(token, "")
            result["answer"] = answer.strip()

        return result

    def predict_batch(
        self,
        images: List[Union[str, Image.Image, np.ndarray]],
        questions: List[str],
        **kwargs,
    ) -> List[Dict[str, Any]]:
        """
        批量预测

        Args:
            images: 图像列表
            questions: 问题列表
            **kwargs: 其他参数

        Returns:
            predictions: 预测结果列表
        """
        if len(images) != len(questions):
            raise ValueError("Number of images and questions must match")

        predictions = []
        for image, question in zip(images, questions):
            try:
                prediction = self.predict(image, question, **kwargs)
                predictions.append(prediction)
            except Exception as e:
                logger.error(f"Error predicting for question '{question}': {e}")
                predictions.append(
                    {
                        "raw_output": "",
                        "question": question,
                        "action": None,
                        "answer": f"Error: {str(e)}",
                        "is_navigation": False,
                    }
                )

        return predictions

    def evaluate_on_dataset(
        self,
        dataset_path: str,
        output_path: Optional[str] = None,
        max_samples: Optional[int] = None,
    ) -> Dict[str, float]:
        """
        在数据集上评估模型

        Args:
            dataset_path: 数据集路径
            output_path: 输出路径
            max_samples: 最大样本数

        Returns:
            metrics: 评估指标
        """
        logger.info(f"Evaluating on dataset: {dataset_path}")

        # 加载数据集
        with open(dataset_path, "r") as f:
            dataset = json.load(f)

        if max_samples:
            dataset = dataset[:max_samples]

        total_samples = 0
        correct_predictions = 0
        navigation_correct = 0
        navigation_total = 0
        text_correct = 0
        text_total = 0

        results = []

        for item in dataset:
            image_path = item.get("image_path", "")
            question = item.get("question", "")
            ground_truth = item.get("answer", "")
            expected_action = item.get("action", "")

            if not os.path.exists(image_path):
                continue

            try:
                # 进行预测
                prediction = self.predict(image_path, question)

                total_samples += 1
                is_correct = False

                # 检查预测是否正确
                if expected_action in ["move_forward", "turn_left", "turn_right"]:
                    # 导航动作
                    navigation_total += 1
                    if prediction["action"] == expected_action:
                        navigation_correct += 1
                        is_correct = True
                else:
                    # 文本答案
                    text_total += 1
                    if ground_truth.lower() in prediction["answer"].lower():
                        text_correct += 1
                        is_correct = True

                if is_correct:
                    correct_predictions += 1

                # 记录结果
                result_item = {
                    "image_path": image_path,
                    "question": question,
                    "ground_truth": ground_truth,
                    "expected_action": expected_action,
                    "prediction": prediction,
                    "is_correct": is_correct,
                }
                results.append(result_item)

            except Exception as e:
                logger.error(f"Error processing item: {e}")
                continue

        # 计算指标
        metrics = {
            "total_samples": total_samples,
            "overall_accuracy": (
                correct_predictions / total_samples if total_samples > 0 else 0.0
            ),
            "navigation_accuracy": (
                navigation_correct / navigation_total if navigation_total > 0 else 0.0
            ),
            "text_accuracy": text_correct / text_total if text_total > 0 else 0.0,
            "navigation_samples": navigation_total,
            "text_samples": text_total,
        }

        logger.info(f"Evaluation results: {metrics}")

        # 保存结果
        if output_path:
            output_data = {"metrics": metrics, "results": results}
            with open(output_path, "w") as f:
                json.dump(output_data, f, indent=2)
            logger.info(f"Results saved to: {output_path}")

        return metrics


def create_inference_demo(model_path: str, image_path: str, question: str):
    """
    创建推理演示

    Args:
        model_path: 模型路径
        image_path: 图像路径
        question: 问题
    """
    print(f"Loading model from: {model_path}")

    try:
        # 创建推理器
        inference = NavigationInference(model_path)

        print(f"Processing image: {image_path}")
        print(f"Question: {question}")

        # 进行预测
        prediction = inference.predict(image_path, question)

        print("\nPrediction Results:")
        print(f"Raw output: {prediction['raw_output']}")
        print(f"Is navigation: {prediction['is_navigation']}")
        print(f"Action: {prediction['action']}")
        print(f"Answer: {prediction['answer']}")

    except Exception as e:
        print(f"Error in demo: {e}")


if __name__ == "__main__":
    # 测试推理模块
    print("Testing NavigationInference...")

    # 注意：需要实际的模型文件才能运行
    # create_inference_demo(
    #     model_path="./results/final_model",
    #     image_path="./test_image.jpg",
    #     question="How do I get to the kitchen?"
    # )
