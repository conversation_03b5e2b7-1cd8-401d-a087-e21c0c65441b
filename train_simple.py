"""
精简的训练脚本 - 只保留核心功能
"""

import torch
from functools import partial
from transformers import (
    Qwen2VLForConditionalGeneration,
    Qwen2VLProcessor,
    TrainingArguments,
    Trainer,
)
from peft import LoraConfig, get_peft_model
from data_loader import NavigationDataset, collate_fn


def setup_model_and_processor(model_path):
    """加载模型和处理器"""
    print("加载模型和处理器...")

    # 加载模型
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True,
    )

    # 加载处理器
    processor = Qwen2VLProcessor.from_pretrained(model_path, trust_remote_code=True)

    # 添加导航token
    navigation_tokens = [
        "<|action_move_forward|>",
        "<|action_turn_left|>",
        "<|action_turn_right|>",
    ]
    processor.tokenizer.add_tokens(navigation_tokens)
    model.resize_token_embeddings(len(processor.tokenizer))

    return model, processor


def setup_lora(model):
    """设置LoRA"""
    print("设置LoRA...")

    lora_config = LoraConfig(
        r=16,
        lora_alpha=32,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
        lora_dropout=0.1,
        bias="none",
        task_type="CAUSAL_LM",
    )

    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()

    return model


def main():
    # 配置参数
    model_path = "data/Qwen2.5-VL-3B-Instruct"
    data_root = "data/finetune"
    split_file = "data/finetune/split.json"
    output_dir = "results"

    # 训练参数
    num_epochs = 1
    batch_size = 2
    learning_rate = 2e-4
    max_samples = 100  # 限制样本数量用于快速测试

    # 加载模型和处理器
    model, processor = setup_model_and_processor(model_path)

    # 设置LoRA
    model = setup_lora(model)

    # 加载数据集
    print("加载数据集...")
    train_dataset = NavigationDataset(
        data_root=data_root,
        split_file=split_file,
        split="train",
        max_samples=max_samples,
    )

    # 创建数据整理函数
    data_collator = partial(collate_fn, processor=processor)

    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=num_epochs,
        per_device_train_batch_size=batch_size,
        learning_rate=learning_rate,
        weight_decay=0.01,
        warmup_ratio=0.1,
        logging_steps=10,
        save_steps=100,
        bf16=True,
        gradient_checkpointing=True,
        remove_unused_columns=False,
        dataloader_pin_memory=False,
    )

    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        data_collator=data_collator,
        tokenizer=processor.tokenizer,
    )

    # 开始训练
    print("开始训练...")
    trainer.train()

    # 保存模型
    print("保存模型...")
    trainer.save_model()

    print("训练完成！")


if __name__ == "__main__":
    main()
