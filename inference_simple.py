"""
精简的推理脚本 - 只保留核心功能
"""

import torch
from transformers import Qwen2VLForConditionalGeneration, Qwen2VLProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info


def load_model_and_processor(base_model_path, lora_path=None):
    """加载模型和处理器"""
    print("加载模型和处理器...")

    # 加载基础模型
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        base_model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True,
    )

    # 加载处理器
    processor = Qwen2VLProcessor.from_pretrained(
        base_model_path, trust_remote_code=True
    )

    # 添加导航token
    navigation_tokens = [
        "<|action_move_forward|>",
        "<|action_turn_left|>",
        "<|action_turn_right|>",
    ]
    processor.tokenizer.add_tokens(navigation_tokens)
    model.resize_token_embeddings(len(processor.tokenizer))

    # 如果有LoRA权重，加载它们
    if lora_path:
        print(f"加载LoRA权重: {lora_path}")
        model = PeftModel.from_pretrained(model, lora_path)

    return model, processor


def predict(model, processor, image_path, question):
    """进行预测"""
    # 创建messages格式
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "image", "image": image_path},
                {"type": "text", "text": question},
            ],
        }
    ]

    # 处理输入
    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )

    image_inputs, video_inputs = process_vision_info(messages)

    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )

    # 移动到GPU
    inputs = inputs.to(model.device)

    # 生成回答
    with torch.no_grad():
        generated_ids = model.generate(
            **inputs,
            max_new_tokens=128,
            do_sample=False,
            temperature=0.7,
            top_p=0.9,
        )

    # 解码输出
    generated_ids_trimmed = [
        out_ids[len(in_ids) :]
        for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]

    output_text = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False,
    )

    return output_text[0]


def main():
    # 配置参数
    base_model_path = "data/Qwen2.5-VL-3B-Instruct"
    lora_path = "results"  # 训练后的LoRA权重路径

    # 测试图像和问题
    image_path = "assets/image.png"  # 替换为实际的测试图像路径
    question = "请根据图像内容，选择下一步的导航动作。"

    # 加载模型
    model, processor = load_model_and_processor(base_model_path, lora_path)

    # 进行预测
    print(f"问题: {question}")
    print(f"图像: {image_path}")

    result = predict(model, processor, image_path, question)
    print(f"预测结果: {result}")


if __name__ == "__main__":
    main()
