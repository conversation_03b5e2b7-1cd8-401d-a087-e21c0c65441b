"""
重构后的主训练脚本
使用新的模块化架构进行训练
"""

import argparse
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

from src.core import (
    get_logger, setup_logger, Config, ConfigManager,
    setup_training_environment, ExceptionContext
)
from src.data import NavigationDataset, create_collate_fn
from src.models import create_model, apply_lora_to_model, get_navigation_tokenizer
from src.training import create_trainer, create_training_arguments, setup_callbacks
from src.utils import validate_system_requirements

# 设置日志
logger = setup_logger("training", level="INFO")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="导航模型训练")

    # 配置文件
    parser.add_argument(
        "--config", type=str, help="配置文件路径"
    )

    # 基础参数
    parser.add_argument(
        "--model_name", type=str, 
        default="data/Qwen2.5-VL-3B-Instruct",
        help="模型名称或路径"
    )
    parser.add_argument(
        "--data_root", type=str, 
        default="data/finetune",
        help="数据根目录"
    )
    parser.add_argument(
        "--split_file", type=str,
        default="data/finetune/split.json",
        help="数据分割文件"
    )
    parser.add_argument(
        "--output_dir", type=str,
        default="results",
        help="输出目录"
    )

    # 训练参数
    parser.add_argument(
        "--num_train_epochs", type=int, default=1,
        help="训练轮数"
    )
    parser.add_argument(
        "--per_device_train_batch_size", type=int, default=2,
        help="每设备批次大小"
    )
    parser.add_argument(
        "--learning_rate", type=float, default=2e-4,
        help="学习率"
    )
    parser.add_argument(
        "--max_samples", type=int, default=-1,
        help="最大样本数，-1表示全部"
    )

    # 模型参数
    parser.add_argument(
        "--use_4bit", action="store_true",
        help="使用4bit量化"
    )
    parser.add_argument(
        "--use_lora", action="store_true", default=True,
        help="使用LoRA微调"
    )
    parser.add_argument(
        "--lora_rank", type=int, default=16,
        help="LoRA rank"
    )

    # 实验跟踪
    parser.add_argument(
        "--use_swanlab", action="store_true",
        help="使用SwanLab"
    )
    parser.add_argument(
        "--use_wandb", action="store_true",
        help="使用WandB"
    )
    parser.add_argument(
        "--project_name", type=str,
        default="navigation-training",
        help="项目名称"
    )

    # 其他参数
    parser.add_argument(
        "--seed", type=int, default=42,
        help="随机种子"
    )
    parser.add_argument(
        "--resume_from_checkpoint", type=str,
        help="恢复检查点路径"
    )

    return parser.parse_args()


def create_config_from_args(args) -> Config:
    """从命令行参数创建配置"""
    config_dict = {
        "model_name": args.model_name,
        "data_root": args.data_root,
        "split_file": args.split_file,
        "output_dir": args.output_dir,
        "num_train_epochs": args.num_train_epochs,
        "per_device_train_batch_size": args.per_device_train_batch_size,
        "learning_rate": args.learning_rate,
        "use_4bit": args.use_4bit,
        "use_lora": args.use_lora,
        "lora_rank": args.lora_rank,
        "seed": args.seed,
    }
    
    return Config(**config_dict)


def main():
    """主函数"""
    try:
        # 解析参数
        args = parse_args()
        
        # 创建配置
        if args.config:
            config_manager = ConfigManager(args.config)
            config = config_manager.load_config()
            # 用命令行参数覆盖配置文件
            for key, value in vars(args).items():
                if value is not None and hasattr(config, key):
                    setattr(config, key, value)
        else:
            config = create_config_from_args(args)
        
        logger.info("配置加载完成")
        logger.info(f"模型: {config.model_name}")
        logger.info(f"数据: {config.data_root}")
        logger.info(f"输出: {config.output_dir}")
        
        # 验证系统要求
        logger.info("验证系统要求...")
        system_check = validate_system_requirements()
        if system_check["errors"]:
            for error in system_check["errors"]:
                logger.error(error)
            return 1
        
        # 设置训练环境
        logger.info("设置训练环境...")
        env_info = setup_training_environment(seed=config.seed)
        logger.info(f"环境设置完成: {env_info['cuda_device_count']} GPU(s) 可用")
        
        # 创建数据集
        logger.info("加载训练数据...")
        train_dataset = NavigationDataset(
            data_root=config.data_root,
            split_file=config.split_file,
            split="train",
            max_length=config.max_length,
            max_samples=args.max_samples
        )
        logger.info(f"训练数据加载完成: {len(train_dataset)} 样本")
        
        # 创建验证数据集（可选）
        try:
            eval_dataset = NavigationDataset(
                data_root=config.data_root,
                split_file=config.split_file,
                split="val",
                max_length=config.max_length,
                max_samples=100  # 限制验证集大小
            )
            logger.info(f"验证数据加载完成: {len(eval_dataset)} 样本")
        except:
            eval_dataset = None
            logger.warning("未找到验证数据，跳过验证")
        
        # 创建模型
        logger.info("加载模型...")
        model_wrapper = create_model(
            model_name=config.model_name,
            torch_dtype=config.torch_dtype,
            device_map=config.device_map,
            trust_remote_code=config.trust_remote_code
        )
        
        # 加载模型组件
        model_wrapper.load_all()
        model = model_wrapper.get_model()
        tokenizer = model_wrapper.get_tokenizer()
        processor = model_wrapper.get_processor()
        
        # 准备训练模式
        model_wrapper.prepare_for_training()
        
        # 获取导航分词器
        nav_tokenizer = get_navigation_tokenizer(config.model_name)
        nav_tokenizer.resize_model_embeddings(model)
        
        # 应用LoRA
        if config.use_lora:
            logger.info("应用LoRA...")
            model = apply_lora_to_model(
                model,
                r=config.lora_rank,
                lora_alpha=config.lora_alpha,
                lora_dropout=config.lora_dropout,
                target_modules=config.target_modules
            )
        
        # 创建训练参数
        training_args = create_training_arguments(
            output_dir=config.output_dir,
            num_train_epochs=config.num_train_epochs,
            per_device_train_batch_size=config.per_device_train_batch_size,
            learning_rate=config.learning_rate,
            weight_decay=config.weight_decay,
            warmup_ratio=config.warmup_ratio,
            save_steps=config.save_steps,
            logging_steps=config.logging_steps,
            bf16=True,
            gradient_checkpointing=True,
            remove_unused_columns=False,
            seed=config.seed
        )
        
        # 设置回调
        callbacks = setup_callbacks(
            use_swanlab=args.use_swanlab,
            use_wandb=args.use_wandb,
            swanlab_config={"project_name": args.project_name} if args.use_swanlab else None,
            wandb_config={"project_name": args.project_name} if args.use_wandb else None
        )
        
        # 创建训练器
        logger.info("创建训练器...")
        trainer = create_trainer(
            model=model,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            training_args=training_args,
            tokenizer=tokenizer,
            nav_tokenizer=nav_tokenizer,
            callbacks=callbacks
        )
        
        # 开始训练
        logger.info("开始训练...")
        if args.resume_from_checkpoint:
            trainer.train(resume_from_checkpoint=args.resume_from_checkpoint)
        else:
            trainer.train()
        
        # 保存模型
        logger.info("保存模型...")
        trainer.save_model()
        
        logger.info("训练完成！")
        return 0
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
