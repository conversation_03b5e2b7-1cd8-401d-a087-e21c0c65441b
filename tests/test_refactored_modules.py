"""
重构模块测试脚本
测试重构后的各个模块功能
"""

import sys
import os
import tempfile
import unittest
from unittest.mock import Mock, patch

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)

from src.core import Config, get_logger, setup_logger
from src.core.exceptions import NavigationError, ModelLoadError
from src.core.constants import NAVIGATION_TOKENS, ACTION_TOKEN_MAP
from src.utils import validate_system_requirements, get_device_info
from src.utils.file_utils import safe_save_json, safe_load_json, ensure_dir

logger = get_logger("test")


class TestCoreModules(unittest.TestCase):
    """测试核心模块"""

    def test_config_creation(self):
        """测试配置创建"""
        config = Config(
            model_name="test_model", data_root="test_data", learning_rate=1e-4
        )

        self.assertEqual(config.model_name, "test_model")
        self.assertEqual(config.learning_rate, 1e-4)
        self.assertTrue(config.validate())

    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        valid_config = Config(
            model_name="test_model",
            data_root="test_data",
            learning_rate=1e-4,
            lora_rank=16,
        )

        # 模拟路径存在
        with patch("os.path.exists", return_value=True):
            self.assertTrue(valid_config.validate())

        # 无效配置
        invalid_config = Config(
            model_name="test_model",
            data_root="test_data",
            learning_rate=-1,  # 无效学习率
            lora_rank=0,  # 无效rank
        )

        with patch("os.path.exists", return_value=True):
            self.assertFalse(invalid_config.validate())

    def test_logger_setup(self):
        """测试日志设置"""
        logger = setup_logger("test_logger", level="INFO")
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, "test_logger")

    def test_custom_exceptions(self):
        """测试自定义异常"""
        # 测试基础异常
        error = NavigationError(
            "Test error", error_code="TEST001", details={"key": "value"}
        )

        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.error_code, "TEST001")
        self.assertEqual(error.details["key"], "value")

        # 测试模型加载异常
        model_error = ModelLoadError("Model load failed", model_path="/test/path")

        self.assertEqual(model_error.details["model_path"], "/test/path")

    def test_constants(self):
        """测试常量定义"""
        self.assertIn("<move_forward>", NAVIGATION_TOKENS)
        self.assertIn("<turn_left>", NAVIGATION_TOKENS)
        self.assertIn("<turn_right>", NAVIGATION_TOKENS)

        self.assertEqual(ACTION_TOKEN_MAP["move_forward"], "<move_forward>")
        self.assertEqual(ACTION_TOKEN_MAP["turn_left"], "<turn_left>")


class TestUtilModules(unittest.TestCase):
    """测试工具模块"""

    def test_file_utils(self):
        """测试文件工具"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试目录创建
            test_dir = os.path.join(temp_dir, "test_subdir")
            ensure_dir(test_dir)
            self.assertTrue(os.path.exists(test_dir))

            # 测试JSON保存和加载
            test_data = {"key": "value", "number": 42}
            json_file = os.path.join(test_dir, "test.json")

            safe_save_json(test_data, json_file)
            self.assertTrue(os.path.exists(json_file))

            loaded_data = safe_load_json(json_file)
            self.assertEqual(loaded_data, test_data)

    def test_device_utils(self):
        """测试设备工具"""
        device_info = get_device_info()

        self.assertIn("cpu_count", device_info)
        self.assertIn("cuda_available", device_info)
        self.assertIsInstance(device_info["cpu_count"], int)
        self.assertIsInstance(device_info["cuda_available"], bool)

    def test_system_validation(self):
        """测试系统验证"""
        requirements = validate_system_requirements()

        self.assertIn("python_version", requirements)
        self.assertIn("memory_sufficient", requirements)
        self.assertIn("dependencies_available", requirements)


class TestDataModules(unittest.TestCase):
    """测试数据模块"""

    def test_data_loader_creation(self):
        """测试数据加载器创建"""
        from src.data import NavigationDataLoader

        # 模拟数据路径
        with patch("os.path.exists", return_value=True):
            loader = NavigationDataLoader(
                data_root="test_data", split_file="test_split.json"
            )

            self.assertEqual(loader.data_root.name, "test_data")

    def test_data_processor_creation(self):
        """测试数据处理器创建"""
        from src.data import NavigationProcessor

        processor = NavigationProcessor(image_size=(224, 224), max_length=512)

        self.assertEqual(processor.image_processor.image_size, (224, 224))
        self.assertEqual(processor.text_processor.max_length, 512)


class TestModelModules(unittest.TestCase):
    """测试模型模块"""

    def test_model_factory(self):
        """测试模型工厂"""
        from src.models import ModelFactory

        # 测试可用模型类型
        available_models = ModelFactory.get_available_models()
        self.assertIsInstance(available_models, list)
        self.assertIn("qwen2_5_vl", available_models)

        # 测试自动检测模型类型
        model_type = ModelFactory.auto_detect_model_type("Qwen2.5-VL-3B")
        self.assertEqual(model_type, "qwen2_5_vl")

    def test_lora_config(self):
        """测试LoRA配置"""
        from src.models import LoRAConfig

        config = LoRAConfig(r=16, lora_alpha=32, lora_dropout=0.1)

        self.assertTrue(config.validate())
        self.assertEqual(config.r, 16)
        self.assertEqual(config.lora_alpha, 32)

        # 测试无效配置
        invalid_config = LoRAConfig(r=0)  # 无效rank
        self.assertFalse(invalid_config.validate())


class TestTrainingModules(unittest.TestCase):
    """测试训练模块"""

    def test_training_config(self):
        """测试训练配置"""
        from src.training import TrainingConfig

        config = TrainingConfig(
            output_dir="test_output",
            num_train_epochs=1,
            per_device_train_batch_size=2,
            learning_rate=1e-4,
        )

        # 模拟目录创建
        with patch("os.makedirs"):
            config.validate()

        self.assertEqual(config.num_train_epochs, 1)
        self.assertEqual(config.learning_rate, 1e-4)

    def test_training_arguments_creation(self):
        """测试训练参数创建"""
        from src.training import create_training_arguments

        with patch("transformers.TrainingArguments") as mock_args:
            args = create_training_arguments(
                output_dir="test_output", num_train_epochs=1, learning_rate=1e-4
            )

            mock_args.assert_called_once()


def run_tests():
    """运行所有测试"""
    logger.info("开始运行模块测试...")

    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试类
    test_classes = [
        TestCoreModules,
        TestUtilModules,
        TestDataModules,
        TestModelModules,
        TestTrainingModules,
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 报告结果
    if result.wasSuccessful():
        logger.info("✅ 所有测试通过！")
        return True
    else:
        logger.error(
            f"❌ 测试失败: {len(result.failures)} 失败, {len(result.errors)} 错误"
        )
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
