#!/bin/bash

# Qwen2.5-VL 官方工具安装脚本
# 安装 qwen-vl-utils 和其他必要的官方工具

echo "🚀 开始安装 Qwen2.5-VL 官方工具..."

# 检查 Python 环境
if ! command -v python &> /dev/null; then
    echo "❌ Python 未找到，请先安装 Python"
    exit 1
fi

# 检查 pip
if ! command -v pip &> /dev/null; then
    echo "❌ pip 未找到，请先安装 pip"
    exit 1
fi

echo "📦 安装基础依赖..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

echo "📦 安装 Transformers 相关库..."
pip install transformers>=4.37.0 accelerate>=0.26.0 peft>=0.8.0 bitsandbytes>=0.42.0

echo "📦 安装 Qwen-VL 官方工具..."
# 尝试从 PyPI 安装
if pip install qwen-vl-utils; then
    echo "✅ qwen-vl-utils 安装成功"
else
    echo "⚠️  从 PyPI 安装失败，尝试从 GitHub 安装..."
    # 从 GitHub 安装
    if pip install git+https://github.com/QwenLM/Qwen2-VL.git; then
        echo "✅ 从 GitHub 安装成功"
    else
        echo "❌ 安装失败，请手动安装"
        echo "请尝试："
        echo "  pip install git+https://github.com/QwenLM/Qwen2-VL.git"
        exit 1
    fi
fi

echo "📦 安装其他依赖..."
pip install -r requirements.txt

echo "🔧 安装 Flash Attention（可选，用于加速）..."
if pip install flash-attn --no-build-isolation; then
    echo "✅ Flash Attention 安装成功"
else
    echo "⚠️  Flash Attention 安装失败，将使用 eager attention"
fi

echo "✅ 所有依赖安装完成！"
echo ""
echo "🧪 测试安装..."
python -c "
try:
    from qwen_vl_utils import process_vision_info
    print('✅ qwen_vl_utils 导入成功')
except ImportError as e:
    print(f'❌ qwen_vl_utils 导入失败: {e}')

try:
    from transformers import Qwen2_5_VLForConditionalGeneration
    print('✅ Qwen2.5-VL 模型类导入成功')
except ImportError as e:
    print(f'❌ Qwen2.5-VL 模型类导入失败: {e}')

try:
    import flash_attn
    print('✅ Flash Attention 可用')
except ImportError:
    print('⚠️  Flash Attention 不可用，将使用 eager attention')
"

echo ""
echo "🎉 安装完成！现在可以使用官方格式进行训练了。"
echo "💡 提示：如果遇到问题，请查看 README.md 中的故障排除部分。"
