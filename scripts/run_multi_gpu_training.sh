#!/bin/bash

# 多卡训练启动脚本
# 充分利用8张RTX 3090的所有资源

echo "🚀 开始8卡训练 Qwen2.5-VL..."
echo "GPU信息:"
nvidia-smi --query-gpu=index,name,memory.total --format=csv,noheader,nounits

# 设置环境变量优化性能
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=INFO
export NCCL_SOCKET_IFNAME=^docker0,lo
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=0
export NCCL_TREE_THRESHOLD=0

# 设置内存优化
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# 训练参数
DATA_ROOT="data/finetune"
SPLIT_FILE="data/finetune/split.json"
MODEL_NAME="data/Qwen2.5-VL-3B-Instruct"
OUTPUT_DIR="results_8gpu_full"

# 计算最优批次大小
# 每张RTX 3090有24GB显存，我们可以使用较大的批次
PER_DEVICE_BATCH_SIZE=6  # 每卡批次大小
GRADIENT_ACCUMULATION=1  # 梯度累积
# 总有效批次大小 = 6 * 1 * 8 = 48

echo "训练配置:"
echo "- 每设备批次大小: $PER_DEVICE_BATCH_SIZE"
echo "- 梯度累积步数: $GRADIENT_ACCUMULATION"
echo "- 总有效批次大小: $((PER_DEVICE_BATCH_SIZE * GRADIENT_ACCUMULATION * 8))"

# 使用 accelerate launch 启动多卡训练
accelerate launch \
    --config_file accelerate_config.yaml \
    --main_process_port 29500 \
    train_multi_gpu.py \
    --data_root "$DATA_ROOT" \
    --split_file "$SPLIT_FILE" \
    --model_name "$MODEL_NAME" \
    --output_dir "$OUTPUT_DIR" \
    --num_train_epochs 1 \
    --per_device_train_batch_size $PER_DEVICE_BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION \
    --learning_rate 2e-4 \
    --max_length 2048 \
    --save_steps 1000 \
    --logging_steps 50 \
    --max_samples -1

echo "✅ 训练完成！"
