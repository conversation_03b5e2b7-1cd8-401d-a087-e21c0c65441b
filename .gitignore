/.conda/
/data/
/results*
/swanlog/

# Python .gitignore

# -------------------------------------------------------------------
# 1. 字节码和缓存文件 (Byte-compiled / Cache files)
# 这些是 Python 解释器为了加速运行而自动生成的文件。
# -------------------------------------------------------------------
__pycache__/
*.py[cod]
*$py.class
*.so


# -------------------------------------------------------------------
# 2. 虚拟环境 (Virtual Environments)
# 虚拟环境包含了大量的包和文件，每个开发者本地的环境都可能不同。
# -------------------------------------------------------------------
venv/
env/
.venv/
ENV/
venv.bak/
.virtualenv/


# -------------------------------------------------------------------
# 3. 敏感信息和环境变量 (Secrets and Environment Variables)
# !!! 极其重要：永远不要将包含密码、API密钥等敏感信息的文件提交到git仓库。
# -------------------------------------------------------------------
.env
.env.*
! .env.example
# ↑ 这个模式的意思是：忽略所有 .env 文件 (如 .env.local, .env.prod)，
#   但是 .env.example 文件除外，这个文件通常作为配置模板保留。

secrets.yml
*.pem
*.key


# -------------------------------------------------------------------
# 4. 分发/打包 (Distribution / Packaging)
# 这些是在构建、打包和发布 Python 包时生成的文件。
# -------------------------------------------------------------------
build/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST


# -------------------------------------------------------------------
# 5. 测试和代码覆盖率报告 (Testing & Coverage reports)
# 测试过程中产生的缓存和报告。
# -------------------------------------------------------------------
.pytest_cache/
.tox/
.nox/
.coverage
.coverage.*
nosetests.xml
coverage.xml
*.prof
*.lprof
*.prof
*.out
*.pstat
htmlcov/


# -------------------------------------------------------------------
# 6. IDE 和编辑器配置 (IDE and Editor Configurations)
# 避免将个人编辑器设置提交到公共仓库。
# -------------------------------------------------------------------
.idea/
.vscode/
# !.vscode/settings.json        # 如果团队需要统一的VSCode设置，可以取消此行的注释
# !.vscode/launch.json
.project
.pydevproject
.settings/
.spyderproject
.ropeproject


# -------------------------------------------------------------------
# 7. Jupyter Notebook
# Notebook 运行时的检查点文件。
# -------------------------------------------------------------------
.ipynb_checkpoints


# -------------------------------------------------------------------
# 8. 日志和数据库文件 (Logs and Databases)
# -------------------------------------------------------------------
*.log
*.log.*
*.sql
*.sqlite
*.sqlite3
db.sqlite3


# -------------------------------------------------------------------
# 9. 操作系统生成的文件 (OS-generated files)
# -------------------------------------------------------------------
.DS_Store
Thumbs.db  
ehthumbs.db
Desktop.ini


# -------------------------------------------------------------------
# 10. (可选) 数据文件 - 根据你的项目需求取消注释和修改
# 如果你的项目使用大型数据文件，最好不要将它们提交到git。
# -------------------------------------------------------------------
# *.csv
# *.json
# *.parquet
# *.xlsx
# data/