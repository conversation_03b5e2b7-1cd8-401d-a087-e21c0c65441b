# 推理配置文件
# 视觉导航模型推理配置

# 模型配置
model_name: "results/checkpoint-500"  # 训练后的模型路径
device: "auto"
torch_dtype: "bfloat16"
load_in_4bit: false
trust_remote_code: true

# 推理参数
max_new_tokens: 256
temperature: 0.7
do_sample: true
top_p: 0.9
top_k: 50

# 图像处理
image_size: [224, 224]

# 输出配置
output_format: "json"
include_raw_output: true
save_results: true

# 批量推理配置
batch_size: 4
max_batch_samples: 100

# 系统配置
clear_cache_after_inference: true
monitor_memory: true
