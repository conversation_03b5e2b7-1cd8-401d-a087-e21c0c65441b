# 训练配置文件
# 视觉导航模型微调配置

# 模型配置
model_name: "data/Qwen2.5-VL-3B-Instruct"
use_4bit: true
use_lora: true
lora_rank: 16
lora_alpha: 32
lora_dropout: 0.1
target_modules:
  - "q_proj"
  - "k_proj"
  - "v_proj"
  - "o_proj"
  - "gate_proj"
  - "up_proj"
  - "down_proj"

# 数据配置
data_root: "data/finetune"
split_file: "data/finetune/split.json"
max_length: 2048
image_size: [224, 224]

# 训练配置
output_dir: "results"
num_train_epochs: 1
per_device_train_batch_size: 2
per_device_eval_batch_size: 2
gradient_accumulation_steps: 4
learning_rate: 0.0002
weight_decay: 0.01
warmup_ratio: 0.03
lr_scheduler_type: "cosine"
optimizer_type: "adamw"

# 精度和性能
fp16: false
bf16: true
gradient_checkpointing: true
dataloader_pin_memory: false
dataloader_num_workers: 0

# 保存和日志
save_steps: 500
save_total_limit: 3
logging_steps: 10
evaluation_strategy: "no"

# 分布式训练
ddp_find_unused_parameters: false
ddp_timeout: 1800

# 其他设置
remove_unused_columns: false
report_to: "none"
seed: 42
load_best_model_at_end: false

# 系统配置
device_map: "auto"
torch_dtype: "bfloat16"
trust_remote_code: true
