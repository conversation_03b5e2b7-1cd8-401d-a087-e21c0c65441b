# 默认配置文件
# 视觉导航模型微调项目配置

# 模型配置
model_name: "data/Qwen2.5-VL-3B-Instruct"
use_4bit: true
use_lora: true
lora_rank: 16
lora_alpha: 32
lora_dropout: 0.1
target_modules:
  - "q_proj"
  - "k_proj"
  - "v_proj"
  - "o_proj"
  - "gate_proj"
  - "up_proj"
  - "down_proj"

# 数据配置
data_root: "data/finetune"
split_file: "data/finetune/split.json"
max_length: 2048
image_size: [224, 224]

# 训练配置
output_dir: "results"
num_train_epochs: 1
per_device_train_batch_size: 2
gradient_accumulation_steps: 4
learning_rate: 0.0002
weight_decay: 0.01
warmup_ratio: 0.03
save_steps: 500
logging_steps: 10

# 推理配置
max_new_tokens: 256
temperature: 0.7
do_sample: true

# 系统配置
device_map: "auto"
torch_dtype: "bfloat16"
trust_remote_code: true
