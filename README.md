# 精简导航模型微调项目

这是一个基于Qwen2.5-VL的导航模型微调项目的精简版本，只保留最核心的功能。

## 项目结构

```
finetune/
├── data_loader.py         # 数据加载器
├── train_simple.py       # 训练脚本
├── inference_simple.py   # 推理脚本
├── requirements_simple.txt # 依赖包
├── data/                  # 数据目录
└── README.md             # 说明文档
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements_simple.txt
```

### 2. 训练模型

```bash
python train_simple.py
```

### 3. 推理测试

```bash
python inference_simple.py
```

## 核心功能

- ✅ 数据加载和处理
- ✅ LoRA微调
- ✅ 模型训练
- ✅ 推理预测
- ✅ 导航token支持

## 注意事项

1. 确保数据在 `data/finetune` 目录下
2. 根据GPU内存调整批次大小
3. 修改脚本中的路径配置
