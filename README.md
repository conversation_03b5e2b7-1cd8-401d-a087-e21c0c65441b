# 🧭 视觉导航模型微调项目（重构版）

基于 **Qwen2.5-VL-3B** 模型的视觉导航任务微调实现，采用模块化架构设计，支持多 GPU 训练和高效推理。

## ✨ 重构亮点

- 🏗️ **模块化架构**: 清晰的分层设计，每个模块职责单一
- 🔧 **统一配置管理**: 支持YAML配置文件和命令行参数
- 📊 **标准化日志**: 彩色日志输出，支持文件记录
- 🛡️ **异常处理**: 统一的异常处理机制
- 🔄 **代码复用**: 消除重复代码，提高维护性
- 📈 **实验跟踪**: 支持SwanLab和WandB
- 🚀 **性能优化**: 内存管理和批次大小自动优化

## 🏗️ 项目架构

```
finetune/
├── src/                           # 源代码目录
│   ├── core/                      # 核心基础设施
│   │   ├── __init__.py
│   │   ├── config.py              # 统一配置管理
│   │   ├── logger.py              # 统一日志管理
│   │   ├── exceptions.py          # 自定义异常处理
│   │   └── constants.py           # 常量定义
│   ├── data/                      # 数据处理层
│   │   ├── __init__.py
│   │   ├── loaders.py             # 数据加载器
│   │   ├── processors.py          # 数据预处理器
│   │   ├── datasets.py            # 数据集类
│   │   └── collators.py           # 数据整理器
│   ├── models/                    # 模型层
│   │   ├── __init__.py
│   │   ├── base.py                # 基础模型类
│   │   ├── qwen_model.py          # Qwen模型封装
│   │   ├── lora_config.py         # LoRA配置
│   │   └── tokenizers.py          # 分词器管理
│   ├── training/                  # 训练层
│   │   ├── __init__.py
│   │   ├── trainer.py             # 训练器
│   │   ├── callbacks.py           # 训练回调
│   │   ├── arguments.py           # 训练参数
│   │   └── utils.py               # 训练工具函数
│   ├── inference/                 # 推理层
│   │   ├── __init__.py
│   │   ├── predictor.py           # 预测器
│   │   └── utils.py               # 推理工具函数
│   └── utils/                     # 通用工具
│       ├── __init__.py
│       ├── file_utils.py          # 文件操作工具
│       ├── device_utils.py        # 设备管理工具
│       └── validation.py          # 验证工具
├── configs/                       # 配置文件目录
│   ├── default.yaml               # 默认配置
│   ├── training.yaml              # 训练配置
│   └── inference.yaml             # 推理配置
├── data/                          # 数据目录
│   ├── Qwen2.5-VL-3B-Instruct/    # 预训练模型
│   └── finetune/                  # 微调数据
│       ├── HM3D/                  # HM3D数据集
│       ├── Matterport/            # Matterport数据集
│       └── split.json             # 数据分割文件
├── train_new.py                   # 重构后的训练脚本
├── train_multi_gpu_new.py         # 重构后的多GPU训练脚本
├── inference_demo_new.py          # 重构后的推理演示脚本
├── preprocess_data_new.py         # 重构后的数据预处理脚本
└── README_new.md                  # 重构项目说明
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install torch transformers peft accelerate
pip install qwen-vl-utils  # 可选，用于官方工具支持
pip install swanlab wandb  # 可选，用于实验跟踪
```

### 2. 数据预处理

```bash
# 预处理数据
python preprocess_data_new.py \
    --data_root data/finetune \
    --split_file data/finetune/split.json \
    --output_dir processed_data \
    --validate_data \
    --save_processed
```

### 3. 单GPU训练

```bash
# 使用默认配置训练
python train_new.py \
    --model_name data/Qwen2.5-VL-3B-Instruct \
    --data_root data/finetune \
    --split_file data/finetune/split.json \
    --output_dir results \
    --use_lora \
    --use_swanlab

# 使用配置文件训练
python train_new.py --config configs/training.yaml
```

### 4. 多GPU训练

```bash
# 多GPU训练
python train_multi_gpu_new.py \
    --model_name data/Qwen2.5-VL-3B-Instruct \
    --data_root data/finetune \
    --split_file data/finetune/split.json \
    --output_dir results_multi_gpu \
    --per_device_train_batch_size 4 \
    --gradient_accumulation_steps 2 \
    --auto_batch_size
```

### 5. 推理演示

```bash
# 推理演示
python inference_demo_new.py \
    --model_path results/checkpoint-500 \
    --image_path test_image.jpg \
    --question "What should I do next?" \
    --output_file result.json
```

## 📋 配置说明

### 配置文件示例 (configs/training.yaml)

```yaml
# 模型配置
model_name: "data/Qwen2.5-VL-3B-Instruct"
use_4bit: true
use_lora: true
lora_rank: 16
lora_alpha: 32

# 数据配置
data_root: "data/finetune"
split_file: "data/finetune/split.json"
max_length: 2048

# 训练配置
output_dir: "results"
num_train_epochs: 1
per_device_train_batch_size: 2
learning_rate: 0.0002
```

## 🔧 模块说明

### 核心模块 (src/core/)
- **config.py**: 统一的配置管理，支持YAML文件和命令行参数
- **logger.py**: 标准化日志记录，支持彩色输出和文件记录
- **exceptions.py**: 自定义异常类，提供详细的错误信息
- **constants.py**: 项目常量定义

### 数据模块 (src/data/)
- **loaders.py**: 数据加载器，负责从文件系统加载数据
- **processors.py**: 数据预处理器，处理图像和文本数据
- **datasets.py**: 数据集类，封装PyTorch Dataset
- **collators.py**: 数据整理器，处理批量数据

### 模型模块 (src/models/)
- **base.py**: 基础模型类和工厂模式
- **qwen_model.py**: Qwen模型的专门封装
- **lora_config.py**: LoRA配置和管理
- **tokenizers.py**: 分词器管理，包括导航Token处理

### 训练模块 (src/training/)
- **trainer.py**: 导航任务专用训练器
- **arguments.py**: 训练参数配置
- **callbacks.py**: 训练回调函数
- **utils.py**: 训练工具函数

### 推理模块 (src/inference/)
- **predictor.py**: 推理预测器
- **utils.py**: 推理工具函数

## 🎯 主要改进

### 1. 代码结构优化
- 消除了代码重复
- 每个文件职责单一
- 模块间依赖清晰

### 2. 配置管理统一
- 支持YAML配置文件
- 命令行参数覆盖
- 配置验证机制

### 3. 错误处理完善
- 统一的异常处理
- 详细的错误信息
- 优雅的错误恢复

### 4. 日志系统标准化
- 彩色日志输出
- 分级日志记录
- 文件日志支持

### 5. 性能优化
- 内存使用监控
- 批次大小自动优化
- GPU资源管理

## 📊 实验跟踪

支持多种实验跟踪工具：

```bash
# 使用SwanLab
python train_new.py --use_swanlab --project_name my-navigation-project

# 使用WandB
python train_new.py --use_wandb --project_name my-navigation-project
```

## 🔍 调试和监控

### 查看系统要求
```python
from src.utils import validate_system_requirements
print(validate_system_requirements())
```

### 监控GPU内存
```python
from src.utils import monitor_gpu_memory
monitor_gpu_memory(interval=5, duration=60)
```

### 验证数据
```python
from src.utils import validate_training_data
result = validate_training_data(samples)
print(result)
```

## 📝 开发指南

### 添加新的模型类型
1. 在 `src/models/` 下创建新的模型文件
2. 继承 `BaseModel` 类
3. 在工厂类中注册新模型

### 添加新的数据处理器
1. 在 `src/data/processors.py` 中添加新的处理器类
2. 继承 `DataProcessor` 基类
3. 实现 `process_sample` 方法

### 添加新的训练回调
1. 在 `src/training/callbacks.py` 中添加新的回调类
2. 继承 `TrainerCallback` 基类
3. 实现相应的回调方法

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
