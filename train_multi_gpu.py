"""
重构后的多GPU训练脚本
使用新的模块化架构进行多GPU训练
"""

import argparse
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

from src.core import get_logger, setup_logger, Config, ExceptionContext
from src.data import NavigationDataset
from src.models import create_model, apply_lora_to_model, get_navigation_tokenizer
from src.training import create_trainer, create_multi_gpu_training_arguments, setup_callbacks
from src.utils import setup_training_environment, get_device_info, optimize_batch_size

# 设置日志
logger = setup_logger("multi_gpu_training", level="INFO")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="多GPU导航模型训练")
    
    # 基础参数
    parser.add_argument(
        "--model_name", type=str,
        default="data/Qwen2.5-VL-3B-Instruct",
        help="模型名称或路径"
    )
    parser.add_argument(
        "--data_root", type=str,
        default="data/finetune",
        help="数据根目录"
    )
    parser.add_argument(
        "--split_file", type=str,
        default="data/finetune/split.json",
        help="数据分割文件"
    )
    parser.add_argument(
        "--output_dir", type=str,
        default="results_multi_gpu",
        help="输出目录"
    )
    
    # 训练参数
    parser.add_argument(
        "--num_train_epochs", type=int, default=1,
        help="训练轮数"
    )
    parser.add_argument(
        "--per_device_train_batch_size", type=int, default=4,
        help="每设备批次大小"
    )
    parser.add_argument(
        "--gradient_accumulation_steps", type=int, default=2,
        help="梯度累积步数"
    )
    parser.add_argument(
        "--learning_rate", type=float, default=2e-4,
        help="学习率"
    )
    parser.add_argument(
        "--max_length", type=int, default=2048,
        help="最大序列长度"
    )
    parser.add_argument(
        "--max_samples", type=int, default=-1,
        help="最大样本数，-1表示全部"
    )
    
    # 模型参数
    parser.add_argument(
        "--use_lora", action="store_true", default=True,
        help="使用LoRA微调"
    )
    parser.add_argument(
        "--lora_rank", type=int, default=32,
        help="LoRA rank"
    )
    parser.add_argument(
        "--lora_alpha", type=int, default=64,
        help="LoRA alpha"
    )
    
    # 优化参数
    parser.add_argument(
        "--auto_batch_size", action="store_true",
        help="自动优化批次大小"
    )
    parser.add_argument(
        "--seed", type=int, default=42,
        help="随机种子"
    )
    
    return parser.parse_args()


def setup_multi_gpu_environment():
    """设置多GPU环境"""
    try:
        from accelerate import Accelerator
        
        # 初始化Accelerator
        accelerator = Accelerator()
        
        logger.info(f"使用设备: {accelerator.device}")
        logger.info(f"进程数: {accelerator.num_processes}")
        logger.info(f"本地进程索引: {accelerator.local_process_index}")
        
        return accelerator
        
    except ImportError:
        logger.error("Accelerate库未安装，无法进行多GPU训练")
        raise
    except Exception as e:
        logger.error(f"多GPU环境设置失败: {e}")
        raise


def main():
    """主函数"""
    try:
        # 解析参数
        args = parse_args()
        
        logger.info("开始多GPU训练...")
        logger.info(f"参数: {vars(args)}")
        
        # 设置多GPU环境
        accelerator = setup_multi_gpu_environment()
        
        # 设置训练环境
        env_info = setup_training_environment(seed=args.seed)
        device_info = get_device_info()
        
        logger.info(f"可用GPU数量: {device_info.get('cuda_device_count', 0)}")
        
        # 自动优化批次大小
        if args.auto_batch_size and device_info.get('cuda_available', False):
            gpu_memory = device_info['cuda_devices'][0]['total_memory']
            optimal_batch_size = optimize_batch_size(
                model_size=3_000_000_000,  # 3B参数
                available_memory=gpu_memory,
                sequence_length=args.max_length
            )
            
            if optimal_batch_size != args.per_device_train_batch_size:
                logger.info(f"优化批次大小: {args.per_device_train_batch_size} -> {optimal_batch_size}")
                args.per_device_train_batch_size = optimal_batch_size
        
        # 计算有效批次大小
        effective_batch_size = (
            args.per_device_train_batch_size * 
            args.gradient_accumulation_steps * 
            accelerator.num_processes
        )
        logger.info(f"有效批次大小: {effective_batch_size}")
        
        # 创建数据集
        logger.info("加载训练数据...")
        train_dataset = NavigationDataset(
            data_root=args.data_root,
            split_file=args.split_file,
            split="train",
            max_length=args.max_length,
            max_samples=args.max_samples
        )
        logger.info(f"训练数据: {len(train_dataset)} 样本")
        
        # 创建模型
        logger.info("加载模型...")
        model_wrapper = create_model(
            model_name=args.model_name,
            device_map=None,  # 让accelerator处理设备分配
            torch_dtype="bfloat16"
        )
        
        model_wrapper.load_all()
        model = model_wrapper.get_model()
        tokenizer = model_wrapper.get_tokenizer()
        
        # 准备训练模式
        model_wrapper.prepare_for_training()
        
        # 获取导航分词器
        nav_tokenizer = get_navigation_tokenizer(args.model_name)
        nav_tokenizer.resize_model_embeddings(model)
        
        # 应用LoRA
        if args.use_lora:
            logger.info("应用LoRA...")
            model = apply_lora_to_model(
                model,
                r=args.lora_rank,
                lora_alpha=args.lora_alpha,
                target_modules=[
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"
                ]
            )
        
        # 创建多GPU训练参数
        training_args = create_multi_gpu_training_arguments(
            output_dir=args.output_dir,
            num_train_epochs=args.num_train_epochs,
            per_device_train_batch_size=args.per_device_train_batch_size,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
            learning_rate=args.learning_rate,
            seed=args.seed
        )
        
        # 设置回调
        callbacks = setup_callbacks()
        
        # 创建训练器
        logger.info("创建训练器...")
        trainer = create_trainer(
            model=model,
            train_dataset=train_dataset,
            training_args=training_args,
            tokenizer=tokenizer,
            nav_tokenizer=nav_tokenizer,
            callbacks=callbacks
        )
        
        # 显示内存使用情况
        if device_info.get('cuda_available', False):
            for i, gpu_info in enumerate(device_info['cuda_devices']):
                logger.info(
                    f"GPU {i}: {gpu_info['name']}, "
                    f"内存: {gpu_info['total_memory']:.1f}GB"
                )
        
        # 开始训练
        logger.info("🚀 开始多GPU训练...")
        trainer.train()
        
        # 保存模型
        logger.info("保存模型...")
        trainer.save_model()
        
        logger.info("✅ 多GPU训练完成！")
        logger.info(f"模型已保存到: {args.output_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"多GPU训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
